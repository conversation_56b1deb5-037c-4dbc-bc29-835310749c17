#!/usr/bin/env python3
"""
🎯 F2Pool Focused Test
تست متمرکز روی F2Pool با تمام سرورها و پورت‌ها
"""

import socket
import json
import time
import threading
import hashlib
import binascii
import struct
import random

class F2PoolFocusedTester:
    def __init__(self):
        self.username = "pparashm.001"
        self.password = "21235365876986800"
        
        # تمام سرورهای F2Pool
        self.servers = [
            # Universal
            ("btc.f2pool.com", 1314, "Universal Primary"),
            ("btc.f2pool.com", 25, "Universal Backup 1"),
            ("btc.f2pool.com", 3333, "Universal Backup 2"),
            
            # SSL
            ("btcssl.f2pool.com", 1300, "SSL Primary"),
            ("btcssl.f2pool.com", 1301, "SSL Backup"),
            
            # Asia
            ("btc-asia.f2pool.com", 1314, "Asia Primary"),
            ("btc-asia.f2pool.com", 25, "Asia Backup 1"),
            ("btc-asia.f2pool.com", 1315, "Asia Backup 2"),
            ("btc-asia.f2pool.com", 3333, "Asia Backup 3"),
        ]
        
        self.results = {}
    
    def test_server(self, host: str, port: int, description: str):
        """تست یک سرور مشخص"""
        print(f"\n🔍 Testing {description}")
        print(f"Server: {host}:{port}")
        print("-" * 50)
        
        result = {
            'host': host,
            'port': port,
            'description': description,
            'connection': False,
            'subscribe': False,
            'authorize': False,
            'jobs_received': 0,
            'difficulty': None,
            'extranonce1': None,
            'response_type': None,
            'error': None,
            'mining_data': None
        }
        
        try:
            # اتصال
            print("🔌 Connecting...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(15)
            sock.connect((host, port))
            result['connection'] = True
            print("✅ Connected successfully!")
            
            # Subscribe
            print("📝 Subscribing...")
            subscribe_msg = {
                "id": 1,
                "method": "mining.subscribe",
                "params": ["F2PoolTester/1.0"]
            }
            sock.send((json.dumps(subscribe_msg) + "\n").encode())
            
            # دریافت پاسخ subscribe
            subscribe_response = self.receive_response(sock, timeout=10)
            if subscribe_response:
                if subscribe_response.get('id') == 1 and 'result' in subscribe_response:
                    result['subscribe'] = True
                    result['response_type'] = 'JSON'
                    
                    # استخراج اطلاعات subscribe
                    subscribe_result = subscribe_response['result']
                    if len(subscribe_result) >= 2:
                        result['extranonce1'] = subscribe_result[1]
                        print(f"✅ Subscribed! Extranonce1: {result['extranonce1']}")
                    
                elif 'HTTP/' in str(subscribe_response):
                    result['response_type'] = 'HTTP'
                    result['error'] = 'Cloudflare protection detected'
                    print("❌ HTTP response detected (Cloudflare)")
                    sock.close()
                    return result
            else:
                print("❌ No subscribe response")
                sock.close()
                return result
            
            # Authorize
            print("🔐 Authorizing...")
            auth_msg = {
                "id": 2,
                "method": "mining.authorize",
                "params": [self.username, self.password]
            }
            sock.send((json.dumps(auth_msg) + "\n").encode())
            
            # دریافت پاسخ authorize
            auth_response = self.receive_response(sock, timeout=10)
            if auth_response and auth_response.get('id') == 2:
                if auth_response.get('result') == True:
                    result['authorize'] = True
                    print(f"✅ Authorized user: {self.username}")
                else:
                    print(f"❌ Authorization failed: {auth_response}")
                    sock.close()
                    return result
            else:
                print("❌ No authorization response")
                sock.close()
                return result
            
            # انتظار برای دریافت mining data
            print("⏳ Waiting for mining data...")
            mining_data = self.collect_mining_data(sock, duration=30)
            result.update(mining_data)
            
            if result['jobs_received'] > 0:
                print(f"✅ Received {result['jobs_received']} mining jobs")
                print(f"📊 Difficulty: {result['difficulty']}")
                
                # تست ارسال share
                if result['mining_data']:
                    print("🎯 Testing share submission...")
                    share_result = self.test_share_submission(sock, result['mining_data'])
                    result['share_test'] = share_result
            
            sock.close()
            
        except socket.timeout:
            result['error'] = 'Connection timeout'
            print("❌ Connection timeout")
        except ConnectionRefusedError:
            result['error'] = 'Connection refused'
            print("❌ Connection refused")
        except Exception as e:
            result['error'] = str(e)
            print(f"❌ Error: {e}")
        
        return result
    
    def receive_response(self, sock, timeout=10):
        """دریافت پاسخ از سرور"""
        try:
            sock.settimeout(timeout)
            buffer = ""
            
            while True:
                data = sock.recv(4096)
                if not data:
                    break
                
                decoded = data.decode('utf-8', errors='replace')
                
                # چک کردن HTTP response
                if decoded.startswith('HTTP/'):
                    return {'type': 'HTTP', 'data': decoded}
                
                buffer += decoded
                
                # پردازش JSON messages
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    
                    if line:
                        try:
                            return json.loads(line)
                        except json.JSONDecodeError:
                            continue
            
            return None
            
        except socket.timeout:
            return None
        except Exception as e:
            print(f"Receive error: {e}")
            return None
    
    def collect_mining_data(self, sock, duration=30):
        """جمع‌آوری داده‌های استخراج"""
        data = {
            'jobs_received': 0,
            'difficulty': None,
            'mining_data': None
        }
        
        start_time = time.time()
        buffer = ""
        
        try:
            while time.time() - start_time < duration:
                sock.settimeout(2)
                
                try:
                    recv_data = sock.recv(4096)
                    if not recv_data:
                        break
                    
                    buffer += recv_data.decode('utf-8', errors='replace')
                    
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()
                        
                        if line:
                            try:
                                msg = json.loads(line)
                                
                                if msg.get('method') == 'mining.notify':
                                    data['jobs_received'] += 1
                                    params = msg['params']
                                    
                                    if data['jobs_received'] == 1:  # ذخیره اولین job
                                        data['mining_data'] = {
                                            'job_id': params[0],
                                            'prevhash': params[1],
                                            'coinb1': params[2],
                                            'coinb2': params[3],
                                            'merkle_branch': params[4],
                                            'version': params[5],
                                            'nbits': params[6],
                                            'ntime': params[7],
                                            'clean_jobs': params[8] if len(params) > 8 else False
                                        }
                                    
                                    print(f"📋 Job {data['jobs_received']}: {params[0]}")
                                
                                elif msg.get('method') == 'mining.set_difficulty':
                                    if msg.get('params') and len(msg['params']) > 0:
                                        data['difficulty'] = msg['params'][0]
                                        print(f"🎯 Difficulty set: {data['difficulty']}")
                                
                            except json.JSONDecodeError:
                                continue
                
                except socket.timeout:
                    continue
                except Exception as e:
                    print(f"Data collection error: {e}")
                    break
        
        except Exception as e:
            print(f"Collection error: {e}")
        
        return data
    
    def test_share_submission(self, sock, job_data):
        """تست ارسال share"""
        try:
            # ساخت share ساده برای تست
            extranonce2 = "00000001"
            nonce = random.randint(0, 0xFFFFFFFF)
            nonce_hex = f"{nonce:08x}"
            
            share_msg = {
                "id": 100,
                "method": "mining.submit",
                "params": [
                    self.username,
                    job_data['job_id'],
                    extranonce2,
                    job_data['ntime'],
                    nonce_hex
                ]
            }
            
            print(f"📤 Submitting test share: nonce={nonce_hex}")
            sock.send((json.dumps(share_msg) + "\n").encode())
            
            # انتظار پاسخ
            response = self.receive_response(sock, timeout=5)
            if response:
                if response.get('id') == 100:
                    if response.get('result') == True:
                        print("✅ Share accepted!")
                        return {'submitted': True, 'accepted': True}
                    else:
                        error = response.get('error', 'Unknown error')
                        print(f"❌ Share rejected: {error}")
                        return {'submitted': True, 'accepted': False, 'error': error}
            
            print("⏳ No response to share submission")
            return {'submitted': True, 'accepted': None}
            
        except Exception as e:
            print(f"❌ Share submission error: {e}")
            return {'submitted': False, 'error': str(e)}
    
    def run_comprehensive_test(self):
        """اجرای تست جامع روی تمام سرورها"""
        print("🚀 F2Pool Comprehensive Test")
        print(f"Username: {self.username}")
        print(f"Password: {'*' * len(self.password)}")
        print("=" * 70)
        
        working_servers = []
        failed_servers = []
        
        for host, port, description in self.servers:
            result = self.test_server(host, port, description)
            self.results[f"{host}:{port}"] = result
            
            if result['connection'] and result['subscribe'] and result['authorize']:
                working_servers.append(result)
                print(f"✅ {description} - WORKING")
            else:
                failed_servers.append(result)
                print(f"❌ {description} - FAILED")
            
            time.sleep(3)  # فاصله بین تست‌ها
        
        # گزارش نهایی
        self.generate_final_report(working_servers, failed_servers)
    
    def generate_final_report(self, working_servers, failed_servers):
        """تولید گزارش نهایی"""
        print(f"\n📊 FINAL REPORT")
        print("=" * 70)
        
        print(f"✅ Working Servers: {len(working_servers)}")
        for server in working_servers:
            print(f"  🟢 {server['description']}")
            print(f"     {server['host']}:{server['port']}")
            print(f"     Jobs: {server['jobs_received']}, Difficulty: {server['difficulty']}")
            if 'share_test' in server:
                share_status = "✅" if server['share_test'].get('accepted') else "❌"
                print(f"     Share test: {share_status}")
        
        print(f"\n❌ Failed Servers: {len(failed_servers)}")
        for server in failed_servers:
            print(f"  🔴 {server['description']}")
            print(f"     {server['host']}:{server['port']}")
            print(f"     Error: {server.get('error', 'Unknown')}")
        
        # بهترین سرور
        if working_servers:
            best_server = max(working_servers, key=lambda x: x['jobs_received'])
            print(f"\n🏆 Best Server:")
            print(f"  {best_server['description']}")
            print(f"  {best_server['host']}:{best_server['port']}")
            print(f"  Jobs received: {best_server['jobs_received']}")
            print(f"  Difficulty: {best_server['difficulty']}")
        
        # توصیه‌ها
        print(f"\n💡 Recommendations:")
        if working_servers:
            print("  ✅ Your F2Pool credentials are valid and working!")
            print("  🎯 Use the best performing server for mining")
            print("  ⚡ Consider using ASIC hardware for actual mining")
        else:
            print("  ❌ No working servers found")
            print("  🔍 Check your credentials or try different servers")
        
        # آمار کلی
        total_jobs = sum(s['jobs_received'] for s in working_servers)
        if total_jobs > 0:
            print(f"\n📈 Statistics:")
            print(f"  Total jobs received: {total_jobs}")
            print(f"  Average jobs per server: {total_jobs/len(working_servers):.1f}")

def main():
    tester = F2PoolFocusedTester()
    tester.run_comprehensive_test()

if __name__ == "__main__":
    main()
