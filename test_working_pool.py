#!/usr/bin/env python3
"""
Test Working Mining Pool
Tests our simple miner against a working pool
"""

import socket
import json
import time
import threading
import hashlib
import binascii
import struct

class WorkingPoolTester:
    def __init__(self, host: str, port: int):
        self.host = host
        self.port = port
        self.socket = None
        self.running = False
        self.message_id = 1
        
        # Mining data received from pool
        self.extranonce1 = None
        self.extranonce2_size = None
        self.difficulty = 1
        self.current_job = None
        
    def connect(self):
        """Connect to pool"""
        try:
            print(f"🔌 Connecting to {self.host}:{self.port}...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(30)
            self.socket.connect((self.host, self.port))
            print("✅ Connected!")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def send_message(self, method: str, params: list = None):
        """Send JSON-RPC message"""
        message = {
            "id": self.message_id,
            "method": method,
            "params": params or []
        }
        self.message_id += 1
        
        json_str = json.dumps(message) + "\n"
        print(f"📤 SEND: {json_str.strip()}")
        self.socket.send(json_str.encode('utf-8'))
    
    def receive_messages(self):
        """Receive and process messages from pool"""
        buffer = ""
        while self.running:
            try:
                self.socket.settimeout(1)
                data = self.socket.recv(4096)
                if not data:
                    break
                
                buffer += data.decode('utf-8', errors='replace')
                
                # Process complete JSON messages
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    if line:
                        self.process_message(line)
                        
            except socket.timeout:
                continue
            except Exception as e:
                print(f"❌ Receive error: {e}")
                break
    
    def process_message(self, message_str: str):
        """Process received JSON message"""
        try:
            message = json.loads(message_str)
            print(f"📥 RECV: {json.dumps(message, indent=2)}")
            
            # Handle different message types
            if 'method' in message:
                method = message['method']
                params = message.get('params', [])
                
                if method == 'mining.notify':
                    self.handle_mining_notify(params)
                elif method == 'mining.set_difficulty':
                    self.handle_set_difficulty(params)
                    
            elif 'result' in message:
                # Response to our request
                if message['id'] == 1:  # Subscribe response
                    self.handle_subscribe_response(message['result'])
                elif message['id'] == 2:  # Authorize response
                    self.handle_authorize_response(message['result'])
                else:
                    print(f"✅ Response to request {message['id']}: {message['result']}")
                    
        except json.JSONDecodeError:
            print(f"📥 Non-JSON: {message_str}")
        except Exception as e:
            print(f"❌ Error processing message: {e}")
    
    def handle_subscribe_response(self, result):
        """Handle mining.subscribe response"""
        print("🔔 Processing subscribe response...")
        if isinstance(result, list) and len(result) >= 2:
            self.extranonce1 = result[1]
            self.extranonce2_size = result[2] if len(result) > 2 else 4
            print(f"✅ Extranonce1: {self.extranonce1}")
            print(f"✅ Extranonce2 size: {self.extranonce2_size}")
    
    def handle_authorize_response(self, result):
        """Handle mining.authorize response"""
        if result:
            print("✅ Worker authorized successfully!")
        else:
            print("❌ Worker authorization failed!")
    
    def handle_mining_notify(self, params):
        """Handle mining.notify - new work"""
        print("📢 NEW WORK RECEIVED!")
        if len(params) >= 8:
            job = {
                'job_id': params[0],
                'prevhash': params[1],
                'coinb1': params[2],
                'coinb2': params[3],
                'merkle_branch': params[4],
                'version': params[5],
                'nbits': params[6],
                'ntime': params[7],
                'clean_jobs': params[8] if len(params) > 8 else False
            }
            
            self.current_job = job
            print(f"Job ID: {job['job_id']}")
            print(f"Previous Hash: {job['prevhash'][:16]}...")
            print(f"Version: {job['version']}")
            print(f"nBits: {job['nbits']}")
            print(f"nTime: {job['ntime']}")
            print(f"Clean Jobs: {job['clean_jobs']}")
            
            # Start mining this job
            threading.Thread(target=self.mine_job, args=(job,), daemon=True).start()
    
    def handle_set_difficulty(self, params):
        """Handle mining.set_difficulty"""
        if params and len(params) > 0:
            self.difficulty = params[0]
            print(f"🎯 Difficulty set to: {self.difficulty}")
    
    def mine_job(self, job):
        """Mine a specific job (very simplified)"""
        print(f"⛏️ Starting to mine job {job['job_id']}...")
        
        if not self.extranonce1:
            print("❌ No extranonce1 available")
            return
        
        # Create a simple extranonce2
        extranonce2 = "00000000"
        
        # Build coinbase transaction
        coinbase = job['coinb1'] + self.extranonce1 + extranonce2 + job['coinb2']
        print(f"Coinbase: {coinbase[:50]}...")
        
        # Calculate merkle root
        merkle_root = self.calculate_merkle_root(coinbase, job['merkle_branch'])
        print(f"Merkle Root: {merkle_root}")
        
        # Try some nonces (very limited for demo)
        target = 2**(256 - 32)  # Very easy target for demo
        
        for nonce in range(1000):  # Very small range for demo
            if not self.running:
                break
                
            # Build block header
            header = self.build_block_header(
                job['version'], job['prevhash'], merkle_root,
                job['ntime'], job['nbits'], nonce
            )
            
            # Hash the header
            header_bin = binascii.unhexlify(header)
            hash_result = hashlib.sha256(hashlib.sha256(header_bin).digest()).digest()
            hash_int = int.from_bytes(hash_result, 'big')
            
            if nonce % 100 == 0:
                print(f"⚡ Tried nonce {nonce}, hash: {hash_result.hex()[:16]}...")
            
            # Check if we found a share (very relaxed condition for demo)
            if hash_int < target * 1000000:  # Much easier than real mining
                print(f"🎉 Found potential share at nonce {nonce}!")
                self.submit_share(job['job_id'], extranonce2, job['ntime'], nonce)
                break
        
        print(f"⛏️ Finished mining job {job['job_id']}")
    
    def calculate_merkle_root(self, coinbase: str, merkle_branch: list) -> str:
        """Calculate merkle root from coinbase and merkle branch"""
        # Hash coinbase transaction (double SHA256)
        coinbase_bin = binascii.unhexlify(coinbase)
        current_hash = hashlib.sha256(hashlib.sha256(coinbase_bin).digest()).digest()
        
        # Apply merkle branch
        for branch_hash in merkle_branch:
            branch_bin = binascii.unhexlify(branch_hash)
            combined = current_hash + branch_bin
            current_hash = hashlib.sha256(hashlib.sha256(combined).digest()).digest()
        
        return current_hash.hex()
    
    def build_block_header(self, version: str, prevhash: str, merkle_root: str, 
                          ntime: str, nbits: str, nonce: int) -> str:
        """Build block header for hashing"""
        # Convert to little-endian format
        version_le = struct.pack("<I", int(version, 16)).hex()
        
        # Reverse prevhash (it's in big-endian, we need little-endian)
        prevhash_le = ''.join(reversed([prevhash[i:i+2] for i in range(0, len(prevhash), 2)]))
        
        # Reverse merkle root
        merkle_le = ''.join(reversed([merkle_root[i:i+2] for i in range(0, len(merkle_root), 2)]))
        
        ntime_le = struct.pack("<I", int(ntime, 16)).hex()
        nbits_le = struct.pack("<I", int(nbits, 16)).hex()
        nonce_le = struct.pack("<I", nonce).hex()
        
        header = version_le + prevhash_le + merkle_le + ntime_le + nbits_le + nonce_le
        return header
    
    def submit_share(self, job_id: str, extranonce2: str, ntime: str, nonce: int):
        """Submit a mining share"""
        nonce_hex = f"{nonce:08x}"
        params = ["test_user.worker1", job_id, extranonce2, ntime, nonce_hex]
        
        print(f"📤 Submitting share: {params}")
        self.send_message("mining.submit", params)
    
    def start_mining(self, duration: int = 60):
        """Start the mining process"""
        if not self.connect():
            return False
        
        self.running = True
        
        # Start message receiving thread
        receive_thread = threading.Thread(target=self.receive_messages)
        receive_thread.daemon = True
        receive_thread.start()
        
        # Subscribe to mining
        print("\n🔔 Subscribing to mining...")
        self.send_message("mining.subscribe", ["TestMiner/1.0"])
        time.sleep(2)
        
        # Authorize worker
        print("\n🔐 Authorizing worker...")
        self.send_message("mining.authorize", ["test_user.worker1", "x"])
        time.sleep(2)
        
        # Wait for mining work and let it run
        print(f"\n⏳ Mining for {duration} seconds...")
        time.sleep(duration)
        
        self.running = False
        if self.socket:
            self.socket.close()
        
        print("\n✅ Mining session completed!")

def main():
    print("🧪 Testing Working Mining Pool")
    print("=" * 50)
    
    # Test the working pools we found
    working_pools = [
        ("eu.stratum.slushpool.com", 3333),
        ("solo.ckpool.org", 3333),
        ("us-east.stratum.slushpool.com", 3333)
    ]
    
    for host, port in working_pools:
        print(f"\n🏊‍♂️ Testing {host}:{port}")
        print("-" * 40)
        
        tester = WorkingPoolTester(host, port)
        tester.start_mining(duration=30)
        
        print("\nWaiting before next test...")
        time.sleep(3)

if __name__ == "__main__":
    main()
