# تست اتصال به استخرهای استخراج بیتکوین

## خلاصه نتایج

### ✅ استخرهای کاری:
1. **F2Pool** - `btc.f2pool.com:3333`
   - اتصال: ✅ موفق
   - احراز هویت: ✅ موفق با `pparashm.001`
   - پروتکل Stratum: ✅ کاملاً کار می‌کند
   - کارهای دریافتی: 9 job در 90 ثانیه
   - سختی: 65,536

2. **Slush Pool** - `eu.stratum.slushpool.com:3333`
   - اتصال: ✅ موفق
   - پروتکل Stratum: ✅ کار می‌کند

3. **CK Pool** - `solo.ckpool.org:3333`
   - اتصال: ✅ موفق
   - پروتکل Stratum: ✅ کار می‌کند

### ❌ استخرهای غیرکاری:
- **Antpool**: محافظت Cloudflare
- **F2Pool پورت‌های دیگر**: محافظت Cloudflare
- **Binance Pool**: محافظت Cloudflare
- **ViaBTC**: محافظت Cloudflare

## تحلیل تکنیکی

### پروتکل Stratum:
```json
// Subscribe
{"id": 1, "method": "mining.subscribe", "params": ["F2PoolMiner/1.0"]}

// Response
{
  "id": 1,
  "result": [
    [["mining.notify", "mining.notify"], ["mining.set_difficulty", "mining.set_difficulty"]],
    "00",  // extranonce1
    8      // extranonce2_size
  ]
}

// Authorize
{"id": 2, "method": "mining.authorize", "params": ["pparashm.001", "21235365876986800"]}

// Response
{"id": 2, "result": true}
```

### داده‌های استخراج:
- **Target**: `000000000000ffff000000000000000000000000000000000000000000000000`
- **نرخ هش CPU**: ~75 KH/s
- **احتمال پیدا کردن share**: بسیار کم با CPU

## نتیجه‌گیری

✅ **F2Pool کاملاً کار می‌کند** با اطلاعات ارائه شده:
- Host: `btc.f2pool.com:3333`
- Username: `pparashm.001`
- Password: `21235365876986800`

برای استخراج واقعی نیاز به:
1. **ASIC Miner** (نه CPU)
2. **نرخ هش بالا** (TH/s نه KH/s)
3. **نرم‌افزار تخصصی** مثل BFGMiner یا CGMiner

## فایل‌های ایجاد شده:
1. `f2pool_real_test.py` - تست کامل F2Pool
2. `pool_scanner.py` - اسکن استخرهای مختلف
3. `simple_miner.py` - miner ساده آموزشی
4. `antpool_test.py` - تست Antpool
5. `raw_tcp_test.py` - تست TCP خام
