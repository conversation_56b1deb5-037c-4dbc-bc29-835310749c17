#!/usr/bin/env python3
"""
🧠 Ultimate Hashrate Experiment
آخرین تلاش خلاقانه برای تأثیر بر تشخیص hashrate
"""

import socket
import json
import time
import threading
import hashlib
import binascii
import struct
import random
from concurrent.futures import ThreadPoolExecutor
import multiprocessing

class UltimateHashrateExperiment:
    def __init__(self):
        self.username = "pparashm.001"
        self.password = "21235365876986800"
        
        # بهترین سرور از تست قبلی
        self.best_servers = [
            ("btc.f2pool.com", 25),
            ("btc-asia.f2pool.com", 1315),
            ("btc-asia.f2pool.com", 3333)
        ]
        
        self.experiment_results = {}
    
    def experiment_rapid_valid_shares(self, host, port):
        """آزمایش: ارسال سریع share های معتبر"""
        print(f"\n🚀 Experiment: Rapid Valid Shares on {host}:{port}")
        print("-" * 50)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((host, port))
            
            # Setup connection
            self.setup_connection(sock)
            
            # دریافت job
            job_data = self.get_mining_job(sock)
            if not job_data:
                print("❌ No job received")
                return False
            
            print(f"📋 Job: {job_data['job_id']}")
            print(f"🎯 Difficulty: {job_data.get('difficulty', 'Unknown')}")
            
            # محاسبه share های معتبر سریع
            print("⚡ Computing valid shares rapidly...")
            
            valid_shares = []
            start_time = time.time()
            
            # استفاده از الگوریتم بهینه برای پیدا کردن share
            for extranonce2_int in range(1, 1000):
                extranonce2 = f"{extranonce2_int:08x}"
                
                # محاسبه target آسان‌تر
                easy_target = self.calculate_easy_target(job_data.get('difficulty', 65536))
                
                for nonce in range(100000):
                    if self.is_valid_share(job_data, extranonce2, nonce, easy_target):
                        valid_shares.append({
                            'extranonce2': extranonce2,
                            'nonce': nonce,
                            'nonce_hex': f"{nonce:08x}"
                        })
                        
                        if len(valid_shares) >= 10:  # پیدا کردن 10 share
                            break
                
                if len(valid_shares) >= 10:
                    break
            
            compute_time = time.time() - start_time
            print(f"⏱️ Found {len(valid_shares)} shares in {compute_time:.2f}s")
            
            if not valid_shares:
                print("❌ No valid shares found")
                return False
            
            # ارسال سریع share ها
            print("📤 Submitting shares rapidly...")
            submission_times = []
            
            for i, share in enumerate(valid_shares):
                submit_start = time.time()
                
                success = self.submit_share(
                    sock, job_data['job_id'], 
                    share['extranonce2'], job_data['ntime'], 
                    share['nonce_hex']
                )
                
                submit_time = time.time() - submit_start
                submission_times.append(submit_time)
                
                if success:
                    print(f"✅ Share {i+1} submitted in {submit_time:.3f}s")
                else:
                    print(f"❌ Share {i+1} failed")
                
                # فاصله کوتاه بین share ها
                time.sleep(0.1)
            
            # بررسی پاسخ‌ها
            print("📥 Checking responses...")
            responses = self.collect_responses(sock, timeout=10)
            
            accepted = sum(1 for r in responses if r.get('result') == True)
            rejected = sum(1 for r in responses if r.get('result') == False)
            
            print(f"📊 Results: {accepted} accepted, {rejected} rejected")
            
            # محاسبه hashrate ظاهری
            total_time = sum(submission_times)
            apparent_hashrate = len(valid_shares) * 65536 * (2**32) / total_time if total_time > 0 else 0
            
            print(f"⚡ Apparent hashrate: {apparent_hashrate/1e12:.2f} TH/s")
            
            sock.close()
            
            return {
                'shares_found': len(valid_shares),
                'shares_accepted': accepted,
                'shares_rejected': rejected,
                'apparent_hashrate': apparent_hashrate,
                'success': accepted > 0
            }
            
        except Exception as e:
            print(f"❌ Experiment failed: {e}")
            return False
    
    def experiment_parallel_workers(self, host, port):
        """آزمایش: worker های موازی"""
        print(f"\n🔗 Experiment: Parallel Workers on {host}:{port}")
        print("-" * 50)
        
        def worker_function(worker_id):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(20)
                sock.connect((host, port))
                
                # هر worker با نام متفاوت
                worker_name = f"{self.username}_{worker_id}"
                
                # Setup
                self.setup_connection_with_name(sock, worker_name)
                
                # دریافت job
                job_data = self.get_mining_job(sock)
                if not job_data:
                    return {'worker_id': worker_id, 'success': False}
                
                # ارسال یک share
                extranonce2 = f"{worker_id:08x}"
                
                # پیدا کردن share معتبر
                for nonce in range(worker_id * 10000, (worker_id + 1) * 10000):
                    if self.is_valid_share(job_data, extranonce2, nonce):
                        nonce_hex = f"{nonce:08x}"
                        success = self.submit_share(
                            sock, job_data['job_id'], 
                            extranonce2, job_data['ntime'], nonce_hex
                        )
                        
                        if success:
                            # بررسی پاسخ
                            response = self.get_single_response(sock)
                            accepted = response.get('result') == True if response else False
                            
                            sock.close()
                            return {
                                'worker_id': worker_id,
                                'success': True,
                                'accepted': accepted,
                                'nonce': nonce_hex
                            }
                
                sock.close()
                return {'worker_id': worker_id, 'success': False}
                
            except Exception as e:
                return {'worker_id': worker_id, 'success': False, 'error': str(e)}
        
        # راه‌اندازی 5 worker موازی
        print("🚀 Starting 5 parallel workers...")
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(worker_function, i) for i in range(5)]
            results = [f.result() for f in futures]
        
        successful_workers = [r for r in results if r['success']]
        accepted_shares = [r for r in successful_workers if r.get('accepted')]
        
        print(f"📊 Results:")
        print(f"  Successful workers: {len(successful_workers)}/5")
        print(f"  Accepted shares: {len(accepted_shares)}")
        
        for result in results:
            status = "✅" if result['success'] else "❌"
            worker_id = result['worker_id']
            
            if result['success'] and result.get('accepted'):
                print(f"  {status} Worker {worker_id}: Share accepted!")
            elif result['success']:
                print(f"  {status} Worker {worker_id}: Share rejected")
            else:
                print(f"  {status} Worker {worker_id}: Failed")
        
        return {
            'total_workers': 5,
            'successful_workers': len(successful_workers),
            'accepted_shares': len(accepted_shares),
            'success': len(accepted_shares) > 0
        }
    
    def experiment_timing_patterns(self, host, port):
        """آزمایش: الگوهای زمان‌بندی مختلف"""
        print(f"\n⏰ Experiment: Timing Patterns on {host}:{port}")
        print("-" * 50)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((host, port))
            
            self.setup_connection(sock)
            job_data = self.get_mining_job(sock)
            
            if not job_data:
                return False
            
            patterns = [
                ("Burst", [0.1, 0.1, 0.1, 0.1, 0.1]),  # سریع
                ("Regular", [2, 2, 2, 2, 2]),           # منظم
                ("Random", [1, 4, 0.5, 3, 1.5]),        # تصادفی
                ("Accelerating", [5, 3, 2, 1, 0.5]),    # تسریع
            ]
            
            results = {}
            
            for pattern_name, intervals in patterns:
                print(f"🔄 Testing {pattern_name} pattern...")
                
                pattern_results = []
                
                for i, interval in enumerate(intervals):
                    extranonce2 = f"{pattern_name[:2]}{i:06x}"
                    
                    # پیدا کردن share
                    for nonce in range(i * 1000, (i + 1) * 1000):
                        if self.is_valid_share(job_data, extranonce2, nonce):
                            nonce_hex = f"{nonce:08x}"
                            
                            submit_time = time.time()
                            success = self.submit_share(
                                sock, job_data['job_id'],
                                extranonce2, job_data['ntime'], nonce_hex
                            )
                            
                            if success:
                                response = self.get_single_response(sock, timeout=3)
                                accepted = response.get('result') == True if response else False
                                
                                pattern_results.append({
                                    'interval': interval,
                                    'accepted': accepted,
                                    'time': submit_time
                                })
                                
                                print(f"  Share {i+1}: {'✅' if accepted else '❌'}")
                                break
                    
                    # انتظار بر اساس الگو
                    if i < len(intervals) - 1:
                        time.sleep(interval)
                
                results[pattern_name] = pattern_results
            
            sock.close()
            
            # تحلیل نتایج
            print(f"\n📊 Pattern Analysis:")
            for pattern_name, pattern_results in results.items():
                accepted_count = sum(1 for r in pattern_results if r['accepted'])
                total_count = len(pattern_results)
                
                print(f"  {pattern_name}: {accepted_count}/{total_count} accepted")
            
            return results
            
        except Exception as e:
            print(f"❌ Timing experiment failed: {e}")
            return False
    
    def run_ultimate_experiments(self):
        """اجرای تمام آزمایش‌های نهایی"""
        print("🧠 ULTIMATE HASHRATE EXPERIMENTS")
        print("=" * 70)
        print("Testing the most creative approaches to influence hashrate detection")
        print()
        
        all_results = {}
        
        for host, port in self.best_servers:
            print(f"\n🎯 Testing {host}:{port}")
            print("=" * 50)
            
            server_results = {}
            
            # آزمایش 1: Share های سریع
            result1 = self.experiment_rapid_valid_shares(host, port)
            server_results['rapid_shares'] = result1
            
            time.sleep(5)
            
            # آزمایش 2: Worker های موازی
            result2 = self.experiment_parallel_workers(host, port)
            server_results['parallel_workers'] = result2
            
            time.sleep(5)
            
            # آزمایش 3: الگوهای زمان‌بندی
            result3 = self.experiment_timing_patterns(host, port)
            server_results['timing_patterns'] = result3
            
            all_results[f"{host}:{port}"] = server_results
            
            time.sleep(10)  # فاصله بین سرورها
        
        # تحلیل نهایی
        self.analyze_ultimate_results(all_results)
        
        return all_results
    
    def analyze_ultimate_results(self, results):
        """تحلیل نهایی نتایج"""
        print(f"\n🎯 ULTIMATE ANALYSIS")
        print("=" * 70)
        
        total_experiments = 0
        successful_experiments = 0
        
        for server, server_results in results.items():
            print(f"\n📊 {server}:")
            
            for exp_name, exp_result in server_results.items():
                total_experiments += 1
                
                if exp_result and exp_result != False:
                    if isinstance(exp_result, dict) and exp_result.get('success'):
                        successful_experiments += 1
                        print(f"  ✅ {exp_name}: SUCCESS")
                        
                        if 'apparent_hashrate' in exp_result:
                            hashrate_th = exp_result['apparent_hashrate'] / 1e12
                            print(f"     Apparent hashrate: {hashrate_th:.2f} TH/s")
                        
                        if 'shares_accepted' in exp_result:
                            print(f"     Shares accepted: {exp_result['shares_accepted']}")
                    
                    elif isinstance(exp_result, dict):
                        print(f"  ⚠️ {exp_name}: Partial success")
                    else:
                        print(f"  ❌ {exp_name}: Failed")
                else:
                    print(f"  ❌ {exp_name}: Failed")
        
        print(f"\n📈 FINAL STATISTICS:")
        print(f"Total experiments: {total_experiments}")
        print(f"Successful experiments: {successful_experiments}")
        print(f"Success rate: {successful_experiments/total_experiments*100:.1f}%")
        
        if successful_experiments > 0:
            print(f"\n🎉 BREAKTHROUGH ACHIEVED!")
            print("Some creative approaches showed promise!")
            print("Further investigation and optimization recommended.")
        else:
            print(f"\n🔒 COMPLETE SECURITY")
            print("All creative approaches were blocked by pool security.")
            print("F2Pool has very robust anti-manipulation measures.")
    
    # Helper methods
    def setup_connection(self, sock):
        """راه‌اندازی اتصال استاندارد"""
        return self.setup_connection_with_name(sock, self.username)
    
    def setup_connection_with_name(self, sock, username):
        """راه‌اندازی اتصال با نام مشخص"""
        # Subscribe
        subscribe_msg = {"id": 1, "method": "mining.subscribe", "params": ["UltimateTest/1.0"]}
        sock.send((json.dumps(subscribe_msg) + "\n").encode())
        time.sleep(1)
        
        # Authorize
        auth_msg = {"id": 2, "method": "mining.authorize", "params": [username, self.password]}
        sock.send((json.dumps(auth_msg) + "\n").encode())
        time.sleep(2)
        
        return True
    
    def get_mining_job(self, sock, timeout=15):
        """دریافت job استخراج"""
        try:
            sock.settimeout(timeout)
            buffer = ""
            job_data = None
            difficulty = None
            
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                data = sock.recv(4096)
                if not data:
                    break
                
                buffer += data.decode('utf-8', errors='replace')
                
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    
                    if line:
                        try:
                            msg = json.loads(line)
                            
                            if msg.get('method') == 'mining.notify':
                                params = msg['params']
                                job_data = {
                                    'job_id': params[0],
                                    'prevhash': params[1],
                                    'coinb1': params[2],
                                    'coinb2': params[3],
                                    'merkle_branch': params[4],
                                    'version': params[5],
                                    'nbits': params[6],
                                    'ntime': params[7],
                                    'difficulty': difficulty
                                }
                                
                                if difficulty is not None:
                                    return job_data
                            
                            elif msg.get('method') == 'mining.set_difficulty':
                                if msg.get('params'):
                                    difficulty = msg['params'][0]
                                    
                                    if job_data is not None:
                                        job_data['difficulty'] = difficulty
                                        return job_data
                        
                        except json.JSONDecodeError:
                            continue
            
            # اگر difficulty دریافت نشد، از مقدار پیش‌فرض استفاده کن
            if job_data and job_data.get('difficulty') is None:
                job_data['difficulty'] = 65536
            
            return job_data
            
        except Exception as e:
            print(f"Error getting job: {e}")
            return None
    
    def calculate_easy_target(self, difficulty):
        """محاسبه target آسان‌تر برای تست"""
        base_target = 0x00000000FFFF0000000000000000000000000000000000000000000000000000
        return base_target // max(difficulty // 1000, 1)  # آسان‌تر کردن
    
    def is_valid_share(self, job_data, extranonce2, nonce, target=None):
        """بررسی معتبر بودن share (ساده‌شده)"""
        try:
            if target is None:
                target = self.calculate_easy_target(job_data.get('difficulty', 65536))
            
            # شبیه‌سازی محاسبه hash
            header_data = f"{job_data['version']}{job_data['prevhash']}{extranonce2}{job_data['ntime']}{job_data['nbits']}{nonce:08x}"
            hash_result = hashlib.sha256(header_data.encode()).digest()
            hash_int = int.from_bytes(hash_result, 'big')
            
            return hash_int < target
            
        except:
            return False
    
    def submit_share(self, sock, job_id, extranonce2, ntime, nonce_hex):
        """ارسال share"""
        try:
            share_msg = {
                "id": random.randint(100, 999),
                "method": "mining.submit",
                "params": [self.username, job_id, extranonce2, ntime, nonce_hex]
            }
            sock.send((json.dumps(share_msg) + "\n").encode())
            return True
        except:
            return False
    
    def get_single_response(self, sock, timeout=5):
        """دریافت یک پاسخ"""
        try:
            sock.settimeout(timeout)
            buffer = ""
            
            while True:
                data = sock.recv(4096)
                if not data:
                    break
                
                buffer += data.decode('utf-8', errors='replace')
                
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    
                    if line:
                        try:
                            msg = json.loads(line)
                            if 'result' in msg or 'error' in msg:
                                return msg
                        except:
                            continue
            
            return None
            
        except:
            return None
    
    def collect_responses(self, sock, timeout=10):
        """جمع‌آوری چندین پاسخ"""
        responses = []
        start_time = time.time()
        
        try:
            sock.settimeout(1)
            buffer = ""
            
            while time.time() - start_time < timeout:
                try:
                    data = sock.recv(4096)
                    if not data:
                        break
                    
                    buffer += data.decode('utf-8', errors='replace')
                    
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()
                        
                        if line:
                            try:
                                msg = json.loads(line)
                                if 'result' in msg or 'error' in msg:
                                    responses.append(msg)
                            except:
                                continue
                
                except socket.timeout:
                    continue
        
        except:
            pass
        
        return responses

def main():
    experiment = UltimateHashrateExperiment()
    results = experiment.run_ultimate_experiments()

if __name__ == "__main__":
    main()
