#!/usr/bin/env python3
"""
🎯 F2Pool Port 3333 Focused Test
تست متمرکز روی btc.f2pool.com:3333 با تمام تکنیک‌های خلاقانه
"""

import socket
import json
import time
import threading
import hashlib
import binascii
import struct
import random
from concurrent.futures import ThreadPoolExecutor

class F2Pool3333Tester:
    def __init__(self):
        self.host = "btc.f2pool.com"
        self.port = 3333
        self.username = "pparashm.001"
        self.password = "21235365876986800"

        self.connection_data = {}
        self.mining_data = {}
        self.experiment_results = {}

    def basic_connection_test(self):
        """تست اتصال پایه"""
        print("🔌 Basic Connection Test")
        print("-" * 40)

        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(15)

            connect_start = time.time()
            sock.connect((self.host, self.port))
            connect_time = time.time() - connect_start

            print(f"✅ Connected in {connect_time:.3f}s")

            # Subscribe
            subscribe_start = time.time()
            subscribe_msg = {
                "id": 1,
                "method": "mining.subscribe",
                "params": ["F2Pool3333Test/1.0"]
            }
            sock.send((json.dumps(subscribe_msg) + "\n").encode())

            # دریافت پاسخ subscribe
            subscribe_response = self.receive_json_response(sock, expected_id=1)
            subscribe_time = time.time() - subscribe_start

            if subscribe_response and 'result' in subscribe_response:
                print(f"✅ Subscribed in {subscribe_time:.3f}s")

                result = subscribe_response['result']
                if len(result) >= 2:
                    extranonce1 = result[1]
                    extranonce2_size = result[2] if len(result) > 2 else 4

                    print(f"📝 Extranonce1: {extranonce1}")
                    print(f"📝 Extranonce2 size: {extranonce2_size}")

                    self.connection_data = {
                        'extranonce1': extranonce1,
                        'extranonce2_size': extranonce2_size,
                        'subscriptions': result[0]
                    }
            else:
                print("❌ Subscribe failed")
                return False

            # Authorize
            auth_start = time.time()
            auth_msg = {
                "id": 2,
                "method": "mining.authorize",
                "params": [self.username, self.password]
            }
            sock.send((json.dumps(auth_msg) + "\n").encode())

            auth_response = self.receive_json_response(sock, expected_id=2)
            auth_time = time.time() - auth_start

            if auth_response and auth_response.get('result') == True:
                print(f"✅ Authorized in {auth_time:.3f}s")
            else:
                print(f"❌ Authorization failed: {auth_response}")
                return False

            # دریافت mining data
            print("⏳ Collecting mining data...")
            mining_data = self.collect_mining_data(sock, duration=20)
            self.mining_data = mining_data

            if mining_data['jobs_received'] > 0:
                print(f"📋 Received {mining_data['jobs_received']} jobs")
                print(f"🎯 Difficulty: {mining_data['difficulty']}")

            sock.close()
            return True

        except Exception as e:
            print(f"❌ Connection test failed: {e}")
            return False

    def experiment_1_rapid_shares(self):
        """آزمایش 1: ارسال سریع share ها"""
        print("\n🚀 Experiment 1: Rapid Share Submission")
        print("-" * 40)

        if not self.mining_data or not self.mining_data.get('current_job'):
            print("❌ No mining data available")
            return False

        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))

            # Setup connection
            self.setup_mining_connection(sock)

            job = self.mining_data['current_job']
            print(f"📋 Using job: {job['job_id']}")

            # تولید share های متعدد
            shares = []
            print("⚡ Generating multiple shares...")

            for i in range(10):
                extranonce2 = f"{i+1:08x}"

                # پیدا کردن nonce معتبر (ساده‌شده)
                for nonce in range(i * 1000, (i + 1) * 1000):
                    if self.is_potentially_valid_share(job, extranonce2, nonce):
                        shares.append({
                            'extranonce2': extranonce2,
                            'nonce': f"{nonce:08x}",
                            'ntime': job['ntime']
                        })
                        break

            print(f"📊 Generated {len(shares)} shares")

            # ارسال سریع
            print("📤 Submitting shares rapidly...")
            submission_results = []

            for i, share in enumerate(shares):
                submit_start = time.time()

                share_msg = {
                    "id": 100 + i,
                    "method": "mining.submit",
                    "params": [
                        self.username,
                        job['job_id'],
                        share['extranonce2'],
                        share['ntime'],
                        share['nonce']
                    ]
                }

                sock.send((json.dumps(share_msg) + "\n").encode())
                submit_time = time.time() - submit_start

                submission_results.append({
                    'share_id': i,
                    'submit_time': submit_time,
                    'nonce': share['nonce']
                })

                print(f"  📤 Share {i+1}: {share['nonce']} ({submit_time:.3f}s)")

                # فاصله کوتاه
                time.sleep(0.05)

            # جمع‌آوری پاسخ‌ها
            print("📥 Collecting responses...")
            responses = self.collect_share_responses(sock, expected_count=len(shares))

            accepted = sum(1 for r in responses if r.get('result') == True)
            rejected = sum(1 for r in responses if r.get('result') == False)

            print(f"📊 Results: {accepted} accepted, {rejected} rejected, {len(responses)} total responses")

            # محاسبه hashrate ظاهری
            total_time = sum(r['submit_time'] for r in submission_results)
            if total_time > 0:
                apparent_hashrate = len(shares) * 65536 * (2**32) / total_time
                print(f"⚡ Apparent hashrate: {apparent_hashrate/1e12:.2f} TH/s")

            sock.close()

            return {
                'shares_submitted': len(shares),
                'shares_accepted': accepted,
                'shares_rejected': rejected,
                'apparent_hashrate': apparent_hashrate/1e12 if total_time > 0 else 0
            }

        except Exception as e:
            print(f"❌ Rapid shares experiment failed: {e}")
            return False

    def experiment_2_multiple_connections(self):
        """آزمایش 2: اتصالات متعدد همزمان"""
        print("\n🔗 Experiment 2: Multiple Simultaneous Connections")
        print("-" * 40)

        def connection_worker(worker_id):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(20)
                sock.connect((self.host, self.port))

                # هر connection با worker متفاوت
                worker_name = f"{self.username}_{worker_id}"

                # Subscribe
                subscribe_msg = {
                    "id": 1,
                    "method": "mining.subscribe",
                    "params": [f"MultiWorker{worker_id}/1.0"]
                }
                sock.send((json.dumps(subscribe_msg) + "\n").encode())
                time.sleep(1)

                # Authorize
                auth_msg = {
                    "id": 2,
                    "method": "mining.authorize",
                    "params": [worker_name, self.password]
                }
                sock.send((json.dumps(auth_msg) + "\n").encode())
                time.sleep(2)

                # دریافت job
                job_data = self.get_job_from_socket(sock)

                if job_data:
                    # ارسال یک share
                    extranonce2 = f"w{worker_id:07x}"

                    for nonce in range(worker_id * 5000, (worker_id + 1) * 5000):
                        if self.is_potentially_valid_share(job_data, extranonce2, nonce):
                            share_msg = {
                                "id": 100,
                                "method": "mining.submit",
                                "params": [
                                    worker_name,
                                    job_data['job_id'],
                                    extranonce2,
                                    job_data['ntime'],
                                    f"{nonce:08x}"
                                ]
                            }

                            sock.send((json.dumps(share_msg) + "\n").encode())

                            # بررسی پاسخ
                            response = self.receive_json_response(sock, timeout=5)
                            accepted = response.get('result') == True if response else False

                            sock.close()

                            return {
                                'worker_id': worker_id,
                                'success': True,
                                'share_accepted': accepted,
                                'nonce': f"{nonce:08x}"
                            }

                sock.close()
                return {'worker_id': worker_id, 'success': False}

            except Exception as e:
                return {'worker_id': worker_id, 'success': False, 'error': str(e)}

        print("🚀 Starting 8 simultaneous connections...")

        with ThreadPoolExecutor(max_workers=8) as executor:
            futures = [executor.submit(connection_worker, i) for i in range(8)]
            results = [f.result() for f in futures]

        successful = [r for r in results if r['success']]
        accepted_shares = [r for r in successful if r.get('share_accepted')]

        print(f"📊 Results:")
        print(f"  Successful connections: {len(successful)}/8")
        print(f"  Accepted shares: {len(accepted_shares)}")

        for result in results:
            status = "✅" if result['success'] else "❌"
            worker_id = result['worker_id']

            if result['success'] and result.get('share_accepted'):
                print(f"  {status} Worker {worker_id}: Share accepted! ({result.get('nonce', 'N/A')})")
            elif result['success']:
                print(f"  {status} Worker {worker_id}: Connected but share rejected")
            else:
                error = result.get('error', 'Unknown error')
                print(f"  {status} Worker {worker_id}: Failed ({error})")

        return {
            'total_connections': 8,
            'successful_connections': len(successful),
            'accepted_shares': len(accepted_shares)
        }

    def experiment_3_timing_manipulation(self):
        """آزمایش 3: دستکاری timing"""
        print("\n⏰ Experiment 3: Timing Manipulation")
        print("-" * 40)

        if not self.mining_data or not self.mining_data.get('current_job'):
            print("❌ No mining data available")
            return False

        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))

            self.setup_mining_connection(sock)

            job = self.mining_data['current_job']

            # تست الگوهای مختلف timing
            patterns = [
                ("Ultra Fast", 0.01),    # خیلی سریع
                ("Fast", 0.1),           # سریع
                ("Normal", 1.0),         # عادی
                ("Slow", 5.0),           # آهسته
                ("Variable", None)       # متغیر
            ]

            results = {}

            for pattern_name, interval in patterns:
                print(f"🔄 Testing {pattern_name} pattern...")

                if pattern_name == "Variable":
                    intervals = [0.1, 2.0, 0.5, 3.0, 0.2]
                else:
                    intervals = [interval] * 5

                pattern_results = []

                for i, wait_time in enumerate(intervals):
                    extranonce2 = f"{pattern_name[:2]}{i:06x}"

                    # پیدا کردن share
                    for nonce in range(i * 2000, (i + 1) * 2000):
                        if self.is_potentially_valid_share(job, extranonce2, nonce):

                            # ارسال share
                            share_msg = {
                                "id": 200 + i,
                                "method": "mining.submit",
                                "params": [
                                    self.username,
                                    job['job_id'],
                                    extranonce2,
                                    job['ntime'],
                                    f"{nonce:08x}"
                                ]
                            }

                            submit_time = time.time()
                            sock.send((json.dumps(share_msg) + "\n").encode())

                            # بررسی پاسخ
                            response = self.receive_json_response(sock, timeout=3)
                            accepted = response.get('result') == True if response else False

                            pattern_results.append({
                                'interval': wait_time,
                                'accepted': accepted,
                                'submit_time': submit_time
                            })

                            print(f"  Share {i+1}: {'✅' if accepted else '❌'} (interval: {wait_time}s)")
                            break

                    # انتظار
                    if i < len(intervals) - 1:
                        time.sleep(wait_time)

                results[pattern_name] = pattern_results

            sock.close()

            # تحلیل نتایج
            print(f"\n📊 Timing Analysis:")
            for pattern_name, pattern_results in results.items():
                accepted_count = sum(1 for r in pattern_results if r['accepted'])
                total_count = len(pattern_results)
                acceptance_rate = accepted_count / total_count * 100 if total_count > 0 else 0

                print(f"  {pattern_name}: {accepted_count}/{total_count} ({acceptance_rate:.1f}%)")

            return results

        except Exception as e:
            print(f"❌ Timing experiment failed: {e}")
            return False

    def experiment_4_share_flooding(self):
        """آزمایش 4: غرق کردن pool با share ها"""
        print("\n🌊 Experiment 4: Share Flooding")
        print("-" * 40)

        if not self.mining_data or not self.mining_data.get('current_job'):
            print("❌ No mining data available")
            return False

        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))

            self.setup_mining_connection(sock)

            job = self.mining_data['current_job']
            print(f"📋 Flooding with shares for job: {job['job_id']}")

            # ارسال تعداد زیادی share
            flood_count = 50
            print(f"🌊 Sending {flood_count} shares as fast as possible...")

            start_time = time.time()

            for i in range(flood_count):
                extranonce2 = f"flood{i:03x}"
                nonce = random.randint(0, 0xFFFFFFFF)

                share_msg = {
                    "id": 300 + i,
                    "method": "mining.submit",
                    "params": [
                        self.username,
                        job['job_id'],
                        extranonce2,
                        job['ntime'],
                        f"{nonce:08x}"
                    ]
                }

                sock.send((json.dumps(share_msg) + "\n").encode())

                if i % 10 == 0:
                    print(f"  📤 Sent {i+1}/{flood_count} shares...")

            flood_time = time.time() - start_time
            rate = flood_count / flood_time

            print(f"⚡ Sent {flood_count} shares in {flood_time:.2f}s ({rate:.1f} shares/sec)")

            # جمع‌آوری پاسخ‌ها
            print("📥 Collecting flood responses...")
            responses = self.collect_share_responses(sock, expected_count=flood_count, timeout=15)

            accepted = sum(1 for r in responses if r.get('result') == True)
            rejected = sum(1 for r in responses if r.get('result') == False)

            print(f"📊 Flood Results:")
            print(f"  Shares sent: {flood_count}")
            print(f"  Responses received: {len(responses)}")
            print(f"  Accepted: {accepted}")
            print(f"  Rejected: {rejected}")
            print(f"  Send rate: {rate:.1f} shares/sec")

            sock.close()

            return {
                'shares_sent': flood_count,
                'responses_received': len(responses),
                'accepted': accepted,
                'rejected': rejected,
                'send_rate': rate
            }

        except Exception as e:
            print(f"❌ Share flooding failed: {e}")
            return False

    def run_all_experiments(self):
        """اجرای تمام آزمایش‌ها"""
        print("🎯 F2Pool Port 3333 Comprehensive Testing")
        print(f"Target: {self.host}:{self.port}")
        print(f"Username: {self.username}")
        print("=" * 60)

        # تست اتصال پایه
        if not self.basic_connection_test():
            print("❌ Basic connection failed. Aborting experiments.")
            return

        print("\n" + "="*60)
        print("🧪 STARTING CREATIVE EXPERIMENTS")
        print("="*60)

        experiments = [
            ("Rapid Share Submission", self.experiment_1_rapid_shares),
            ("Multiple Connections", self.experiment_2_multiple_connections),
            ("Timing Manipulation", self.experiment_3_timing_manipulation),
            ("Share Flooding", self.experiment_4_share_flooding)
        ]

        results = {}

        for exp_name, exp_func in experiments:
            print(f"\n🔬 Running: {exp_name}")
            try:
                result = exp_func()
                results[exp_name] = result

                if result and result != False:
                    print(f"✅ {exp_name}: SUCCESS")
                else:
                    print(f"❌ {exp_name}: FAILED")

            except Exception as e:
                print(f"❌ {exp_name}: EXCEPTION - {e}")
                results[exp_name] = False

            time.sleep(3)  # فاصله بین آزمایش‌ها

        # تحلیل نهایی
        self.analyze_final_results(results)

    def analyze_final_results(self, results):
        """تحلیل نهایی نتایج"""
        print(f"\n🎯 FINAL ANALYSIS")
        print("=" * 60)

        successful_experiments = 0
        total_experiments = len(results)

        print("📊 Experiment Results:")

        for exp_name, result in results.items():
            if result and result != False:
                successful_experiments += 1
                print(f"  ✅ {exp_name}: SUCCESS")

                # نمایش جزئیات
                if isinstance(result, dict):
                    for key, value in result.items():
                        if isinstance(value, (int, float)):
                            print(f"     {key}: {value}")
            else:
                print(f"  ❌ {exp_name}: FAILED")

        success_rate = successful_experiments / total_experiments * 100

        print(f"\n📈 Overall Statistics:")
        print(f"  Total experiments: {total_experiments}")
        print(f"  Successful: {successful_experiments}")
        print(f"  Success rate: {success_rate:.1f}%")

        # نتیجه‌گیری
        print(f"\n💡 Conclusions:")

        if successful_experiments == 0:
            print("  🔒 Complete Security: All experiments blocked")
            print("  🛡️ F2Pool has very strong anti-manipulation measures")
            print("  ⚡ Only real hashrate with valid shares will be counted")

        elif successful_experiments < total_experiments // 2:
            print("  🔐 Strong Security: Most experiments blocked")
            print("  ⚠️ Some techniques showed partial success")
            print("  🔍 Further investigation might reveal opportunities")

        else:
            print("  🎉 Breakthrough Achieved!")
            print("  💡 Multiple techniques showed promise")
            print("  🚀 Optimization and refinement recommended")

        print(f"\n🎯 Final Recommendation:")
        print("  For legitimate mining, use proper ASIC hardware")
        print("  Current CPU hashrate (~75 KH/s) is not profitable")
        print("  Consider upgrading to modern mining equipment")

    # Helper methods
    def receive_json_response(self, sock, expected_id=None, timeout=10):
        """دریافت پاسخ JSON"""
        try:
            sock.settimeout(timeout)
            buffer = ""

            while True:
                data = sock.recv(4096)
                if not data:
                    break

                buffer += data.decode('utf-8', errors='replace')

                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()

                    if line:
                        try:
                            msg = json.loads(line)
                            if expected_id is None or msg.get('id') == expected_id:
                                return msg
                        except json.JSONDecodeError:
                            continue

            return None

        except socket.timeout:
            return None
        except Exception:
            return None

    def collect_mining_data(self, sock, duration=20):
        """جمع‌آوری داده‌های استخراج"""
        data = {
            'jobs_received': 0,
            'difficulty': None,
            'current_job': None
        }

        start_time = time.time()
        buffer = ""

        try:
            while time.time() - start_time < duration:
                sock.settimeout(2)

                try:
                    recv_data = sock.recv(4096)
                    if not recv_data:
                        break

                    buffer += recv_data.decode('utf-8', errors='replace')

                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()

                        if line:
                            try:
                                msg = json.loads(line)

                                if msg.get('method') == 'mining.notify':
                                    data['jobs_received'] += 1
                                    params = msg['params']

                                    data['current_job'] = {
                                        'job_id': params[0],
                                        'prevhash': params[1],
                                        'coinb1': params[2],
                                        'coinb2': params[3],
                                        'merkle_branch': params[4],
                                        'version': params[5],
                                        'nbits': params[6],
                                        'ntime': params[7],
                                        'clean_jobs': params[8] if len(params) > 8 else False
                                    }

                                elif msg.get('method') == 'mining.set_difficulty':
                                    if msg.get('params') and len(msg['params']) > 0:
                                        data['difficulty'] = msg['params'][0]

                            except json.JSONDecodeError:
                                continue

                except socket.timeout:
                    continue
                except Exception:
                    break

        except Exception:
            pass

        return data

    def setup_mining_connection(self, sock):
        """راه‌اندازی اتصال استخراج"""
        # Subscribe
        subscribe_msg = {
            "id": 1,
            "method": "mining.subscribe",
            "params": ["F2Pool3333/1.0"]
        }
        sock.send((json.dumps(subscribe_msg) + "\n").encode())
        time.sleep(1)

        # Authorize
        auth_msg = {
            "id": 2,
            "method": "mining.authorize",
            "params": [self.username, self.password]
        }
        sock.send((json.dumps(auth_msg) + "\n").encode())
        time.sleep(2)

    def get_job_from_socket(self, sock, timeout=15):
        """دریافت job از socket"""
        try:
            sock.settimeout(timeout)
            buffer = ""

            start_time = time.time()

            while time.time() - start_time < timeout:
                data = sock.recv(4096)
                if not data:
                    break

                buffer += data.decode('utf-8', errors='replace')

                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()

                    if line:
                        try:
                            msg = json.loads(line)

                            if msg.get('method') == 'mining.notify':
                                params = msg['params']
                                return {
                                    'job_id': params[0],
                                    'prevhash': params[1],
                                    'coinb1': params[2],
                                    'coinb2': params[3],
                                    'merkle_branch': params[4],
                                    'version': params[5],
                                    'nbits': params[6],
                                    'ntime': params[7]
                                }
                        except json.JSONDecodeError:
                            continue

            return None

        except Exception:
            return None

    def is_potentially_valid_share(self, job, extranonce2, nonce):
        """بررسی احتمال معتبر بودن share (ساده‌شده برای تست)"""
        try:
            # شبیه‌سازی ساده - در واقعیت پیچیده‌تر است
            header_data = f"{job['version']}{job['prevhash']}{extranonce2}{job['ntime']}{job['nbits']}{nonce:08x}"
            hash_result = hashlib.sha256(header_data.encode()).digest()
            hash_int = int.from_bytes(hash_result, 'big')

            # target آسان برای تست
            easy_target = 0x0000FFFF00000000000000000000000000000000000000000000000000000000

            return hash_int < easy_target

        except:
            return False

    def collect_share_responses(self, sock, expected_count, timeout=10):
        """جمع‌آوری پاسخ‌های share"""
        responses = []
        start_time = time.time()
        buffer = ""

        try:
            while len(responses) < expected_count and time.time() - start_time < timeout:
                sock.settimeout(1)

                try:
                    data = sock.recv(4096)
                    if not data:
                        break

                    buffer += data.decode('utf-8', errors='replace')

                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()

                        if line:
                            try:
                                msg = json.loads(line)
                                if 'result' in msg or 'error' in msg:
                                    responses.append(msg)
                            except json.JSONDecodeError:
                                continue

                except socket.timeout:
                    continue
                except Exception:
                    break

        except Exception:
            pass

        return responses

def main():
    print("🎯 F2Pool Port 3333 Focused Testing")
    print("stratum+tcp://btc.f2pool.com:3333")
    print("=" * 60)

    tester = F2Pool3333Tester()
    tester.run_all_experiments()

if __name__ == "__main__":
    main()