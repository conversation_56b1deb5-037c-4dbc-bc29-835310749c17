#!/usr/bin/env python3
"""
Mining Pool Scanner
Scans different mining pools and ports to find working Stratum servers
"""

import socket
import json
import time
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

class PoolScanner:
    def __init__(self, timeout: int = 10):
        self.timeout = timeout
    
    def test_pool_connection(self, host: str, port: int) -> dict:
        """Test connection to a single pool"""
        result = {
            'host': host,
            'port': port,
            'connected': False,
            'stratum_response': False,
            'response_data': None,
            'error': None,
            'response_time': None
        }
        
        start_time = time.time()
        sock = None
        
        try:
            # Try to connect
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)
            sock.connect((host, port))
            result['connected'] = True
            
            # Send Stratum subscribe message
            subscribe_msg = {
                "id": 1,
                "method": "mining.subscribe",
                "params": ["PoolScanner/1.0"]
            }
            message = json.dumps(subscribe_msg) + "\n"
            sock.send(message.encode('utf-8'))
            
            # Try to receive response
            sock.settimeout(5)
            response = sock.recv(4096).decode('utf-8', errors='replace')
            result['response_time'] = time.time() - start_time
            
            if response:
                result['response_data'] = response.strip()
                
                # Check if it's a valid Stratum response
                if response.startswith('{') and ('result' in response or 'error' in response):
                    result['stratum_response'] = True
                elif 'HTTP/' in response:
                    result['error'] = 'HTTP response (likely Cloudflare)'
                else:
                    result['error'] = 'Unknown response format'
            
        except socket.timeout:
            result['error'] = 'Connection timeout'
        except ConnectionRefusedError:
            result['error'] = 'Connection refused'
        except Exception as e:
            result['error'] = str(e)
        finally:
            if sock:
                sock.close()
            if not result['response_time']:
                result['response_time'] = time.time() - start_time
        
        return result
    
    def scan_pools(self, pools: list, max_workers: int = 10):
        """Scan multiple pools concurrently"""
        print(f"🔍 Scanning {len(pools)} pool endpoints...")
        print("=" * 60)
        
        results = []
        working_pools = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_pool = {
                executor.submit(self.test_pool_connection, host, port): (host, port)
                for host, port in pools
            }
            
            # Process results as they complete
            for future in as_completed(future_to_pool):
                host, port = future_to_pool[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    # Print immediate result
                    status = "✅" if result['stratum_response'] else "❌"
                    print(f"{status} {host}:{port} - {result['response_time']:.2f}s")
                    
                    if result['connected']:
                        if result['stratum_response']:
                            print(f"   🎉 WORKING STRATUM SERVER!")
                            working_pools.append((host, port))
                        elif result['error']:
                            print(f"   ⚠️  {result['error']}")
                        
                        if result['response_data'] and len(result['response_data']) < 200:
                            print(f"   📝 Response: {result['response_data'][:100]}...")
                    else:
                        print(f"   💥 {result['error']}")
                    
                    print()
                    
                except Exception as e:
                    print(f"❌ {host}:{port} - Error: {e}")
        
        return results, working_pools

def get_mining_pools():
    """Get list of mining pools to test"""
    pools = [
        # Bitcoin pools with different ports
        ("stratum-btc.antpool.com", 3333),
        ("stratum-btc.antpool.com", 25),
        ("ss.antpool.com", 3333),
        ("ss.antpool.com", 25),
        
        # F2Pool alternatives
        ("btc-eu.f2pool.com", 1314),
        ("btc-eu.f2pool.com", 3333),
        ("btc-us.f2pool.com", 1314),
        ("btc-us.f2pool.com", 3333),
        
        # Slush Pool
        ("stratum.slushpool.com", 3333),
        ("eu.stratum.slushpool.com", 3333),
        ("us-east.stratum.slushpool.com", 3333),
        
        # ViaBTC
        ("btc.viabtc.com", 3333),
        ("btc.viabtc.com", 25),
        
        # Poolin
        ("btc.ss.poolin.com", 1883),
        ("btc.ss.poolin.com", 443),
        
        # BTC.com
        ("stratum.btc.com", 3333),
        ("stratum.btc.com", 1800),
        
        # Binance Pool
        ("stratum.binance.com", 3333),
        ("stratum.binance.com", 1800),
        
        # Alternative ports that might bypass Cloudflare
        ("ss.antpool.com", 443),
        ("ss.antpool.com", 80),
        ("btc.f2pool.com", 443),
        ("btc.f2pool.com", 80),
        
        # Some smaller pools
        ("solo.ckpool.org", 3333),
        ("btc.kano.is", 3333),
        ("stratum.mining-dutch.nl", 3333),
    ]
    
    return pools

def test_working_pool(host: str, port: int):
    """Test a working pool with full Stratum flow"""
    print(f"\n🧪 Testing full Stratum flow on {host}:{port}")
    print("-" * 50)
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(15)
        sock.connect((host, port))
        print("✅ Connected!")
        
        # Subscribe
        subscribe_msg = {
            "id": 1,
            "method": "mining.subscribe",
            "params": ["TestMiner/1.0"]
        }
        sock.send((json.dumps(subscribe_msg) + "\n").encode('utf-8'))
        
        response = sock.recv(4096).decode('utf-8').strip()
        print(f"📥 Subscribe response: {response}")
        
        if 'result' in response:
            # Try authorize
            auth_msg = {
                "id": 2,
                "method": "mining.authorize",
                "params": ["test_user.worker1", "x"]
            }
            sock.send((json.dumps(auth_msg) + "\n").encode('utf-8'))
            
            auth_response = sock.recv(4096).decode('utf-8').strip()
            print(f"📥 Auth response: {auth_response}")
            
            # Wait for mining.notify
            print("⏳ Waiting for mining.notify...")
            sock.settimeout(10)
            notify_response = sock.recv(4096).decode('utf-8').strip()
            print(f"📥 Notify response: {notify_response}")
        
        sock.close()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

def main():
    print("🔍 Mining Pool Scanner")
    print("Scanning for working Stratum servers...")
    print("=" * 60)
    
    pools = get_mining_pools()
    scanner = PoolScanner(timeout=8)
    
    results, working_pools = scanner.scan_pools(pools, max_workers=15)
    
    print("\n" + "=" * 60)
    print("📊 SCAN RESULTS")
    print("=" * 60)
    
    print(f"Total pools tested: {len(results)}")
    print(f"Working Stratum servers: {len(working_pools)}")
    
    if working_pools:
        print("\n🎉 WORKING POOLS:")
        for host, port in working_pools:
            print(f"  ✅ {host}:{port}")
        
        # Test the first working pool in detail
        if working_pools:
            test_working_pool(working_pools[0][0], working_pools[0][1])
    else:
        print("\n😞 No working Stratum servers found.")
        print("This might be due to:")
        print("  - Cloudflare protection on most pools")
        print("  - Firewall restrictions")
        print("  - Pools requiring specific user agents")
        print("  - Geographic restrictions")

if __name__ == "__main__":
    main()
