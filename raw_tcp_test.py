#!/usr/bin/env python3
"""
Raw TCP Connection Tester for Mining Pools
Tests raw TCP connections and shows exactly what data is exchanged
"""

import socket
import time
import threading
import sys

class RawTCPTester:
    def __init__(self, host: str, port: int, timeout: int = 10):
        self.host = host
        self.port = port
        self.timeout = timeout
        self.socket = None
        self.running = False
        
    def connect(self) -> bool:
        """Establish raw TCP connection"""
        try:
            print(f"Connecting to {self.host}:{self.port}...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            self.socket.connect((self.host, self.port))
            print("✓ TCP Connection established!")
            return True
        except Exception as e:
            print(f"✗ Connection failed: {e}")
            return False
    
    def listen_for_data(self):
        """Listen for incoming data in a separate thread"""
        while self.running and self.socket:
            try:
                self.socket.settimeout(1)
                data = self.socket.recv(4096)
                if data:
                    print(f"[RECEIVED] {len(data)} bytes:")
                    print(f"Raw: {data}")
                    try:
                        decoded = data.decode('utf-8', errors='replace')
                        print(f"Decoded: {repr(decoded)}")
                        print(f"Text: {decoded}")
                    except:
                        print("Could not decode as UTF-8")
                    print("-" * 50)
                else:
                    print("Connection closed by server")
                    break
            except socket.timeout:
                continue
            except Exception as e:
                print(f"Error receiving data: {e}")
                break
    
    def send_stratum_subscribe(self):
        """Send a Stratum mining.subscribe message"""
        message = '{"id": 1, "method": "mining.subscribe", "params": ["miner02/1.0.0"]}\n'
        try:
            print(f"[SENDING] Stratum subscribe: {repr(message)}")
            self.socket.send(message.encode('utf-8'))
            return True
        except Exception as e:
            print(f"Error sending data: {e}")
            return False
    
    def send_raw_data(self, data: str):
        """Send raw data"""
        try:
            print(f"[SENDING] Raw data: {repr(data)}")
            self.socket.send(data.encode('utf-8'))
            return True
        except Exception as e:
            print(f"Error sending data: {e}")
            return False
    
    def test_connection(self, duration: int = 10):
        """Test the connection for a specified duration"""
        if not self.connect():
            return
        
        self.running = True
        
        # Start listening thread
        listen_thread = threading.Thread(target=self.listen_for_data)
        listen_thread.daemon = True
        listen_thread.start()
        
        # Wait a moment to see if server sends anything immediately
        print(f"Waiting {duration} seconds for server response...")
        time.sleep(2)
        
        # Send Stratum subscribe message
        self.send_stratum_subscribe()
        
        # Wait for responses
        time.sleep(duration - 2)
        
        self.running = False
        self.close()
    
    def close(self):
        """Close the connection"""
        self.running = False
        if self.socket:
            self.socket.close()
            print("Connection closed.")

def test_servers():
    """Test various F2Pool servers"""
    servers = [
        ("stratum.f2pool.com", 1314),
        ("stratum.f2pool.com", 3333),
        ("btc.f2pool.com", 1314),
        ("btc.f2pool.com", 3333),
        ("btc.f2pool.com", 25),
    ]
    
    for host, port in servers:
        print(f"\n{'='*60}")
        print(f"Testing {host}:{port}")
        print(f"{'='*60}")
        
        tester = RawTCPTester(host, port, timeout=10)
        tester.test_connection(duration=8)
        
        print("\nWaiting before next test...")
        time.sleep(2)

def main():
    print("Raw TCP Tester for Mining Pool Connections")
    print("This will show exactly what data is exchanged")
    
    try:
        test_servers()
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()
