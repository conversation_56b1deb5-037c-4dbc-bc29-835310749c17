#!/usr/bin/env python3
"""
🚀 آزمایش پاسخ سریع - آیا می‌توان کلک زد؟
Quick Response Experiment - Can we "cheat" by responding fast?
"""

import socket
import json
import time
import threading
import hashlib
import random

class QuickResponseExperiment:
    def __init__(self):
        self.host = "btc.f2pool.com"
        self.port = 3333
        self.username = "pparashm.001"
        self.password = "21235365876986800"
        
    def experiment_1_instant_random_response(self):
        """آزمایش 1: پاسخ فوری با داده‌های تصادفی"""
        print("🚀 آزمایش 1: پاسخ فوری با Share های تصادفی")
        print("-" * 60)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))
            
            # Setup connection
            self.setup_connection(sock)
            
            # دریافت job
            job = self.get_job(sock)
            if not job:
                print("❌ No job received")
                return False
            
            print(f"📋 Job received: {job['job_id']}")
            print("⚡ Sending instant random responses...")
            
            # ارسال فوری share های تصادفی
            responses = []
            
            for i in range(10):
                # داده‌های کاملاً تصادفی
                extranonce2 = f"{random.randint(0, 0xFFFFFFFF):08x}"
                nonce = f"{random.randint(0, 0xFFFFFFFF):08x}"
                
                # ارسال فوری (بدون هیچ محاسبه‌ای)
                start_time = time.time()
                
                share_msg = {
                    "id": 100 + i,
                    "method": "mining.submit",
                    "params": [
                        self.username,
                        job['job_id'],
                        extranonce2,
                        job['ntime'],
                        nonce
                    ]
                }
                
                sock.send((json.dumps(share_msg) + "\n").encode())
                send_time = time.time() - start_time
                
                print(f"  📤 Share {i+1}: {nonce} (sent in {send_time*1000:.1f}ms)")
                
                # فاصله بسیار کوتاه
                time.sleep(0.01)
            
            # جمع‌آوری پاسخ‌ها
            print("\n📥 Collecting responses...")
            time.sleep(5)
            
            responses = self.collect_responses(sock, timeout=10)
            
            accepted = sum(1 for r in responses if r.get('result') == True)
            rejected = sum(1 for r in responses if r.get('result') == False)
            
            print(f"\n📊 Results:")
            print(f"  Shares sent: 10")
            print(f"  Responses received: {len(responses)}")
            print(f"  Accepted: {accepted}")
            print(f"  Rejected: {rejected}")
            
            # تحلیل خطاها
            if responses:
                print(f"\n🔍 Error Analysis:")
                for i, response in enumerate(responses[:5]):  # نمایش 5 پاسخ اول
                    if response.get('error'):
                        error_code = response['error'][0] if isinstance(response['error'], list) else response['error']
                        error_msg = response['error'][1] if isinstance(response['error'], list) and len(response['error']) > 1 else str(response['error'])
                        print(f"  Response {i+1}: Error {error_code} - {error_msg}")
                    elif response.get('result') == False:
                        print(f"  Response {i+1}: Rejected (no error details)")
                    else:
                        print(f"  Response {i+1}: Accepted!")
            
            sock.close()
            
            return {
                'sent': 10,
                'accepted': accepted,
                'rejected': rejected,
                'success_rate': accepted / 10 * 100 if accepted > 0 else 0
            }
            
        except Exception as e:
            print(f"❌ Experiment 1 failed: {e}")
            return False
    
    def experiment_2_job_replay_attack(self):
        """آزمایش 2: استفاده مجدد از job قدیمی"""
        print("\n🔄 آزمایش 2: Job Replay Attack")
        print("-" * 60)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))
            
            self.setup_connection(sock)
            
            # دریافت job اول
            job1 = self.get_job(sock)
            if not job1:
                print("❌ No first job received")
                return False
            
            print(f"📋 First job: {job1['job_id']}")
            
            # انتظار برای job جدید
            print("⏳ Waiting for new job...")
            time.sleep(10)
            
            job2 = self.get_job(sock)
            if not job2:
                print("❌ No second job received")
                return False
            
            print(f"📋 Second job: {job2['job_id']}")
            
            # تلاش برای استفاده از job قدیمی
            print("🎭 Attempting to use old job...")
            
            old_job_responses = []
            
            for i in range(5):
                extranonce2 = f"old{i:05x}"
                nonce = f"{random.randint(0, 0xFFFFFFFF):08x}"
                
                share_msg = {
                    "id": 200 + i,
                    "method": "mining.submit",
                    "params": [
                        self.username,
                        job1['job_id'],  # استفاده از job قدیمی
                        extranonce2,
                        job1['ntime'],
                        nonce
                    ]
                }
                
                sock.send((json.dumps(share_msg) + "\n").encode())
                print(f"  📤 Old job share {i+1}: {nonce}")
                time.sleep(0.1)
            
            # تلاش برای استفاده از job جدید
            print("✅ Using current job...")
            
            new_job_responses = []
            
            for i in range(5):
                extranonce2 = f"new{i:05x}"
                nonce = f"{random.randint(0, 0xFFFFFFFF):08x}"
                
                share_msg = {
                    "id": 300 + i,
                    "method": "mining.submit",
                    "params": [
                        self.username,
                        job2['job_id'],  # استفاده از job جدید
                        extranonce2,
                        job2['ntime'],
                        nonce
                    ]
                }
                
                sock.send((json.dumps(share_msg) + "\n").encode())
                print(f"  📤 New job share {i+1}: {nonce}")
                time.sleep(0.1)
            
            # جمع‌آوری پاسخ‌ها
            print("\n📥 Collecting responses...")
            time.sleep(5)
            
            all_responses = self.collect_responses(sock, timeout=10)
            
            # تحلیل پاسخ‌ها
            old_job_accepted = 0
            new_job_accepted = 0
            old_job_errors = []
            new_job_errors = []
            
            for response in all_responses:
                req_id = response.get('id', 0)
                
                if 200 <= req_id < 300:  # Old job responses
                    if response.get('result') == True:
                        old_job_accepted += 1
                    elif response.get('error'):
                        old_job_errors.append(response['error'])
                
                elif 300 <= req_id < 400:  # New job responses
                    if response.get('result') == True:
                        new_job_accepted += 1
                    elif response.get('error'):
                        new_job_errors.append(response['error'])
            
            print(f"\n📊 Replay Attack Results:")
            print(f"  Old job shares accepted: {old_job_accepted}/5")
            print(f"  New job shares accepted: {new_job_accepted}/5")
            
            if old_job_errors:
                print(f"  Old job errors: {old_job_errors[0] if old_job_errors else 'None'}")
            
            sock.close()
            
            return {
                'old_job_accepted': old_job_accepted,
                'new_job_accepted': new_job_accepted,
                'old_job_blocked': old_job_accepted == 0
            }
            
        except Exception as e:
            print(f"❌ Experiment 2 failed: {e}")
            return False
    
    def experiment_3_timestamp_manipulation(self):
        """آزمایش 3: دستکاری timestamp"""
        print("\n⏰ آزمایش 3: Timestamp Manipulation")
        print("-" * 60)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))
            
            self.setup_connection(sock)
            
            job = self.get_job(sock)
            if not job:
                print("❌ No job received")
                return False
            
            print(f"📋 Job: {job['job_id']}")
            print(f"🕐 Original ntime: {job['ntime']}")
            
            # تست timestamp های مختلف
            timestamp_tests = [
                ("Original", job['ntime']),
                ("Past (-1 hour)", hex(int(job['ntime'], 16) - 3600)[2:]),
                ("Future (+1 hour)", hex(int(job['ntime'], 16) + 3600)[2:]),
                ("Far past (-1 day)", hex(int(job['ntime'], 16) - 86400)[2:]),
                ("Far future (+1 day)", hex(int(job['ntime'], 16) + 86400)[2:])
            ]
            
            results = {}
            
            for test_name, ntime in timestamp_tests:
                print(f"\n🔍 Testing {test_name}: {ntime}")
                
                extranonce2 = f"time{len(results):03x}"
                nonce = f"{random.randint(0, 0xFFFFFFFF):08x}"
                
                share_msg = {
                    "id": 400 + len(results),
                    "method": "mining.submit",
                    "params": [
                        self.username,
                        job['job_id'],
                        extranonce2,
                        ntime,  # timestamp دستکاری شده
                        nonce
                    ]
                }
                
                sock.send((json.dumps(share_msg) + "\n").encode())
                print(f"  📤 Sent with ntime: {ntime}")
                
                time.sleep(1)
            
            # جمع‌آوری پاسخ‌ها
            print("\n📥 Collecting timestamp responses...")
            time.sleep(5)
            
            responses = self.collect_responses(sock, timeout=10)
            
            print(f"\n📊 Timestamp Manipulation Results:")
            for i, (test_name, ntime) in enumerate(timestamp_tests):
                if i < len(responses):
                    response = responses[i]
                    if response.get('result') == True:
                        print(f"  ✅ {test_name}: ACCEPTED")
                    else:
                        error = response.get('error', 'Rejected')
                        print(f"  ❌ {test_name}: {error}")
                else:
                    print(f"  ⏳ {test_name}: No response")
            
            sock.close()
            
            return {
                'tests_performed': len(timestamp_tests),
                'responses_received': len(responses)
            }
            
        except Exception as e:
            print(f"❌ Experiment 3 failed: {e}")
            return False
    
    def experiment_4_burst_submission(self):
        """آزمایش 4: ارسال انفجاری (burst)"""
        print("\n💥 آزمایش 4: Burst Submission")
        print("-" * 60)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))
            
            self.setup_connection(sock)
            
            job = self.get_job(sock)
            if not job:
                print("❌ No job received")
                return False
            
            print(f"📋 Job: {job['job_id']}")
            print("💥 Sending burst of shares...")
            
            # ارسال انفجاری 50 share در کمتر از 1 ثانیه
            burst_count = 50
            start_time = time.time()
            
            for i in range(burst_count):
                extranonce2 = f"burst{i:03x}"
                nonce = f"{random.randint(0, 0xFFFFFFFF):08x}"
                
                share_msg = {
                    "id": 500 + i,
                    "method": "mining.submit",
                    "params": [
                        self.username,
                        job['job_id'],
                        extranonce2,
                        job['ntime'],
                        nonce
                    ]
                }
                
                sock.send((json.dumps(share_msg) + "\n").encode())
                
                if i % 10 == 0:
                    elapsed = time.time() - start_time
                    rate = (i + 1) / elapsed if elapsed > 0 else 0
                    print(f"  📤 Sent {i+1}/{burst_count} shares ({rate:.0f} shares/sec)")
            
            total_time = time.time() - start_time
            final_rate = burst_count / total_time
            
            print(f"\n⚡ Burst completed:")
            print(f"  Shares sent: {burst_count}")
            print(f"  Time taken: {total_time:.3f} seconds")
            print(f"  Rate: {final_rate:.0f} shares/second")
            
            # جمع‌آوری پاسخ‌ها
            print("\n📥 Collecting burst responses...")
            time.sleep(10)
            
            responses = self.collect_responses(sock, timeout=15)
            
            accepted = sum(1 for r in responses if r.get('result') == True)
            rejected = sum(1 for r in responses if r.get('result') == False)
            
            print(f"\n📊 Burst Results:")
            print(f"  Responses received: {len(responses)}/{burst_count}")
            print(f"  Accepted: {accepted}")
            print(f"  Rejected: {rejected}")
            print(f"  Response rate: {len(responses)/burst_count*100:.1f}%")
            
            # تحلیل نوع خطاها
            error_types = {}
            for response in responses:
                if response.get('error'):
                    error = str(response['error'])
                    error_types[error] = error_types.get(error, 0) + 1
            
            if error_types:
                print(f"\n🔍 Error Types:")
                for error, count in error_types.items():
                    print(f"  {error}: {count} times")
            
            sock.close()
            
            return {
                'sent': burst_count,
                'received': len(responses),
                'accepted': accepted,
                'rejected': rejected,
                'rate': final_rate
            }
            
        except Exception as e:
            print(f"❌ Experiment 4 failed: {e}")
            return False
    
    def run_all_quick_response_experiments(self):
        """اجرای تمام آزمایش‌های پاسخ سریع"""
        print("🚀 آزمایش‌های پاسخ سریع - آیا می‌توان کلک زد؟")
        print("=" * 80)
        print("هدف: بررسی امکان فریب سرور با پاسخ‌های سریع و غیرواقعی")
        print("=" * 80)
        
        experiments = [
            ("Instant Random Response", self.experiment_1_instant_random_response),
            ("Job Replay Attack", self.experiment_2_job_replay_attack),
            ("Timestamp Manipulation", self.experiment_3_timestamp_manipulation),
            ("Burst Submission", self.experiment_4_burst_submission)
        ]
        
        results = {}
        
        for exp_name, exp_func in experiments:
            print(f"\n🔬 Running: {exp_name}")
            try:
                result = exp_func()
                results[exp_name] = result
                
                if result and result != False:
                    print(f"✅ {exp_name}: Completed")
                else:
                    print(f"❌ {exp_name}: Failed")
                    
            except Exception as e:
                print(f"❌ {exp_name}: Exception - {e}")
                results[exp_name] = False
            
            time.sleep(3)  # فاصله بین آزمایش‌ها
        
        # تحلیل نهایی
        self.analyze_quick_response_results(results)
        
        return results
    
    def analyze_quick_response_results(self, results):
        """تحلیل نتایج آزمایش‌های پاسخ سریع"""
        print(f"\n🎯 تحلیل نهایی: آیا کلک زدن ممکن است؟")
        print("=" * 80)
        
        print("📊 نتایج آزمایش‌ها:")
        
        total_cheating_success = 0
        total_experiments = len(results)
        
        for exp_name, result in results.items():
            print(f"\n🔍 {exp_name}:")
            
            if result and result != False:
                if exp_name == "Instant Random Response":
                    success_rate = result.get('success_rate', 0)
                    print(f"  📈 Success rate: {success_rate:.1f}%")
                    if success_rate > 0:
                        total_cheating_success += 1
                        print(f"  🎉 Some random shares were accepted!")
                    else:
                        print(f"  🔒 All random shares rejected")
                
                elif exp_name == "Job Replay Attack":
                    old_blocked = result.get('old_job_blocked', True)
                    print(f"  🔄 Old job blocked: {'Yes' if old_blocked else 'No'}")
                    if not old_blocked:
                        total_cheating_success += 1
                        print(f"  ⚠️ Old jobs still accepted!")
                    else:
                        print(f"  🔒 Old jobs properly rejected")
                
                elif exp_name == "Timestamp Manipulation":
                    print(f"  ⏰ Timestamp tests completed")
                    print(f"  🔒 Server likely uses its own time")
                
                elif exp_name == "Burst Submission":
                    rate = result.get('rate', 0)
                    accepted = result.get('accepted', 0)
                    print(f"  💥 Submission rate: {rate:.0f} shares/sec")
                    print(f"  📊 Accepted shares: {accepted}")
                    if accepted > 0:
                        total_cheating_success += 1
                        print(f"  ⚠️ Some burst shares accepted!")
                    else:
                        print(f"  🔒 All burst shares rejected")
            else:
                print(f"  ❌ Experiment failed")
        
        # نتیجه‌گیری
        print(f"\n🎯 نتیجه‌گیری نهایی:")
        print("=" * 50)
        
        if total_cheating_success == 0:
            print("🔒 کلک زدن غیرممکن است!")
            print("✅ تمام تلاش‌های فریب مسدود شدند")
            print("🛡️ سیستم امنیتی بسیار قوی است")
            
            print(f"\n💡 دلایل شکست:")
            print("  • Share های تصادفی hash نامعتبر دارند")
            print("  • Job های قدیمی شناسایی و رد می‌شوند")
            print("  • Timestamp دستکاری تأثیری ندارد")
            print("  • ارسال انفجاری تشخیص داده می‌شود")
            
        else:
            print(f"⚠️ برخی تکنیک‌ها موفق بودند!")
            print(f"🎯 {total_cheating_success}/{total_experiments} آزمایش موفق")
            print(f"🔍 نیاز به بررسی بیشتر...")
        
        print(f"\n🚀 توصیه نهایی:")
        print("  تنها راه معتبر: سرمایه‌گذاری در سخت‌افزار واقعی!")
        print("  ASIC miner خریداری کنید و به صورت قانونی استخراج کنید")
    
    # Helper methods
    def setup_connection(self, sock):
        """راه‌اندازی اتصال"""
        # Subscribe
        subscribe_msg = {
            "id": 1,
            "method": "mining.subscribe",
            "params": ["QuickResponseTest/1.0"]
        }
        sock.send((json.dumps(subscribe_msg) + "\n").encode())
        time.sleep(1)
        
        # Authorize
        auth_msg = {
            "id": 2,
            "method": "mining.authorize",
            "params": [self.username, self.password]
        }
        sock.send((json.dumps(auth_msg) + "\n").encode())
        time.sleep(2)
    
    def get_job(self, sock, timeout=15):
        """دریافت job"""
        try:
            sock.settimeout(timeout)
            buffer = ""
            
            start_time = time.time()
            
            while time.time() - start_time < timeout:
                data = sock.recv(4096)
                if not data:
                    break
                
                buffer += data.decode('utf-8', errors='replace')
                
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    
                    if line:
                        try:
                            msg = json.loads(line)
                            
                            if msg.get('method') == 'mining.notify':
                                params = msg['params']
                                return {
                                    'job_id': params[0],
                                    'prevhash': params[1],
                                    'coinb1': params[2],
                                    'coinb2': params[3],
                                    'merkle_branch': params[4],
                                    'version': params[5],
                                    'nbits': params[6],
                                    'ntime': params[7]
                                }
                        except json.JSONDecodeError:
                            continue
            
            return None
            
        except Exception:
            return None
    
    def collect_responses(self, sock, timeout=10):
        """جمع‌آوری پاسخ‌ها"""
        responses = []
        start_time = time.time()
        buffer = ""
        
        try:
            while time.time() - start_time < timeout:
                sock.settimeout(1)
                
                try:
                    data = sock.recv(4096)
                    if not data:
                        break
                    
                    buffer += data.decode('utf-8', errors='replace')
                    
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()
                        
                        if line:
                            try:
                                msg = json.loads(line)
                                if 'result' in msg or 'error' in msg:
                                    responses.append(msg)
                            except json.JSONDecodeError:
                                continue
                
                except socket.timeout:
                    continue
                except Exception:
                    break
        
        except Exception:
            pass
        
        return responses

def main():
    experiment = QuickResponseExperiment()
    experiment.run_all_quick_response_experiments()

if __name__ == "__main__":
    main()
