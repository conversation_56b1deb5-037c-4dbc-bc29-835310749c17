#!/usr/bin/env python3
"""
Direct F2Pool Connection Tester
Bypasses Cloudflare by connecting directly to F2Pool IPs
"""

import socket
import json
import time
import threading
import subprocess
import sys

class DirectStratumTester:
    def __init__(self, host: str, port: int, timeout: int = 10):
        self.host = host
        self.port = port
        self.timeout = timeout
        self.socket = None
        self.running = False
        
    def resolve_ip(self):
        """Resolve hostname to IP addresses"""
        try:
            result = socket.getaddrinfo(self.host, self.port, socket.AF_INET)
            ips = list(set([addr[4][0] for addr in result]))
            print(f"Resolved {self.host} to IPs: {ips}")
            return ips
        except Exception as e:
            print(f"Failed to resolve {self.host}: {e}")
            return []
    
    def connect_direct(self, ip: str) -> bool:
        """Connect directly to IP address"""
        try:
            print(f"Connecting directly to {ip}:{self.port}...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            self.socket.connect((ip, self.port))
            print("✓ Direct connection established!")
            return True
        except Exception as e:
            print(f"✗ Direct connection failed: {e}")
            return False
    
    def listen_for_data(self):
        """Listen for incoming data"""
        while self.running and self.socket:
            try:
                self.socket.settimeout(1)
                data = self.socket.recv(4096)
                if data:
                    print(f"[RECEIVED] {len(data)} bytes:")
                    try:
                        decoded = data.decode('utf-8', errors='replace')
                        print(f"Data: {decoded.strip()}")
                        
                        # Try to parse as JSON
                        if decoded.strip().startswith('{'):
                            try:
                                json_data = json.loads(decoded.strip())
                                print(f"JSON: {json.dumps(json_data, indent=2)}")
                            except:
                                pass
                    except:
                        print(f"Raw bytes: {data}")
                    print("-" * 50)
                else:
                    print("Connection closed by server")
                    break
            except socket.timeout:
                continue
            except Exception as e:
                print(f"Error receiving data: {e}")
                break
    
    def send_stratum_message(self, method: str, params: list = None, id: int = 1):
        """Send Stratum message"""
        message = {
            "id": id,
            "method": method,
            "params": params or []
        }
        json_message = json.dumps(message) + "\n"
        
        try:
            print(f"[SENDING] {json_message.strip()}")
            self.socket.send(json_message.encode('utf-8'))
            return True
        except Exception as e:
            print(f"Error sending message: {e}")
            return False
    
    def test_stratum_protocol(self, ip: str, duration: int = 15):
        """Test Stratum protocol on direct IP"""
        if not self.connect_direct(ip):
            return False
        
        self.running = True
        
        # Start listening thread
        listen_thread = threading.Thread(target=self.listen_for_data)
        listen_thread.daemon = True
        listen_thread.start()
        
        # Wait for initial server messages
        print("Waiting for initial server messages...")
        time.sleep(3)
        
        # Send mining.subscribe
        print("\nSending mining.subscribe...")
        self.send_stratum_message("mining.subscribe", ["miner02/1.0.0"])
        time.sleep(3)
        
        # Send mining.authorize with dummy credentials
        print("\nSending mining.authorize...")
        self.send_stratum_message("mining.authorize", ["test_user", "x"])
        time.sleep(3)
        
        # Wait for more responses
        print(f"\nWaiting {duration-9} more seconds for responses...")
        time.sleep(max(1, duration-9))
        
        self.running = False
        self.close()
        return True
    
    def close(self):
        """Close connection"""
        self.running = False
        if self.socket:
            self.socket.close()
            print("Connection closed.")

def get_f2pool_ips():
    """Get F2Pool IP addresses using nslookup"""
    hosts = ["btc.f2pool.com", "stratum.f2pool.com"]
    all_ips = set()
    
    for host in hosts:
        try:
            print(f"Resolving {host}...")
            result = socket.getaddrinfo(host, 1314, socket.AF_INET)
            ips = [addr[4][0] for addr in result]
            all_ips.update(ips)
            print(f"{host} -> {ips}")
        except Exception as e:
            print(f"Failed to resolve {host}: {e}")
    
    return list(all_ips)

def test_direct_connections():
    """Test direct connections to F2Pool IPs"""
    print("Getting F2Pool IP addresses...")
    ips = get_f2pool_ips()
    
    if not ips:
        print("No IPs found!")
        return
    
    ports = [1314, 3333, 25]
    
    for ip in ips:
        for port in ports:
            print(f"\n{'='*60}")
            print(f"Testing direct connection to {ip}:{port}")
            print(f"{'='*60}")
            
            tester = DirectStratumTester("", port)
            success = tester.test_stratum_protocol(ip, duration=12)
            
            if not success:
                print(f"Failed to test {ip}:{port}")
            
            print("\nWaiting before next test...")
            time.sleep(2)

def main():
    print("Direct F2Pool Stratum Tester")
    print("This bypasses Cloudflare by connecting directly to IPs")
    
    try:
        test_direct_connections()
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()
