#!/usr/bin/env python3
"""
Antpool Real Connection Test
Tests Antpool with real credentials
"""

import socket
import json
import time
import threading
import hashlib
import binascii
import struct

class AntpoolMiner:
    def __init__(self, host: str, port: int, username: str, password: str):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        
        self.socket = None
        self.running = False
        self.message_id = 1
        
        # Mining data
        self.extranonce1 = None
        self.extranonce2_size = None
        self.difficulty = 1
        self.current_job = None
        
        # Statistics
        self.shares_submitted = 0
        self.shares_accepted = 0
        self.shares_rejected = 0
        self.jobs_received = 0
        
    def connect(self):
        """Connect to Antpool"""
        try:
            print(f"🔌 Connecting to {self.host}:{self.port}...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(30)
            self.socket.connect((self.host, self.port))
            print("✅ Connected to Antpool!")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def send_message(self, method: str, params: list = None):
        """Send JSON-RPC message to Antpool"""
        message = {
            "id": self.message_id,
            "method": method,
            "params": params or []
        }
        self.message_id += 1
        
        try:
            json_str = json.dumps(message) + "\n"
            print(f"📤 SEND: {json_str.strip()}")
            self.socket.send(json_str.encode('utf-8'))
            return True
        except Exception as e:
            print(f"❌ Send error: {e}")
            return False
    
    def receive_messages(self):
        """Receive and process messages from Antpool"""
        buffer = ""
        while self.running:
            try:
                self.socket.settimeout(2)
                data = self.socket.recv(4096)
                if not data:
                    print("❌ Connection closed by Antpool")
                    break
                
                decoded = data.decode('utf-8', errors='replace')
                
                # Check if it's HTTP response (Cloudflare)
                if decoded.startswith('HTTP/'):
                    print("❌ Received HTTP response (likely Cloudflare protection)")
                    print(f"Response: {decoded[:300]}...")
                    break
                
                buffer += decoded
                
                # Process complete JSON messages
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    if line:
                        self.process_message(line)
                        
            except socket.timeout:
                continue
            except Exception as e:
                print(f"❌ Receive error: {e}")
                break
    
    def process_message(self, message_str: str):
        """Process received JSON message"""
        try:
            message = json.loads(message_str)
            print(f"📥 RECV: {json.dumps(message, indent=2)}")
            
            # Handle different message types
            if 'method' in message:
                method = message['method']
                params = message.get('params', [])
                
                if method == 'mining.notify':
                    self.handle_mining_notify(params)
                elif method == 'mining.set_difficulty':
                    self.handle_set_difficulty(params)
                elif method == 'mining.set_extranonce':
                    self.handle_set_extranonce(params)
                else:
                    print(f"🔔 Unknown method: {method}")
                    
            elif 'result' in message:
                # Response to our request
                request_id = message.get('id')
                result = message['result']
                
                if request_id == 1:  # Subscribe response
                    self.handle_subscribe_response(result)
                elif request_id == 2:  # Authorize response
                    self.handle_authorize_response(result)
                else:
                    # Share submission response
                    if result:
                        self.shares_accepted += 1
                        print(f"✅ Share ACCEPTED! (Total: {self.shares_accepted})")
                    else:
                        self.shares_rejected += 1
                        error = message.get('error')
                        print(f"❌ Share REJECTED! Error: {error} (Total rejected: {self.shares_rejected})")
                        
            elif 'error' in message:
                print(f"❌ Error from Antpool: {message['error']}")
                
        except json.JSONDecodeError:
            print(f"📥 Non-JSON data: {message_str}")
        except Exception as e:
            print(f"❌ Error processing message: {e}")
    
    def handle_subscribe_response(self, result):
        """Handle mining.subscribe response"""
        print("🔔 Processing Antpool subscribe response...")
        try:
            if isinstance(result, list) and len(result) >= 2:
                # Antpool format: [subscriptions, extranonce1, extranonce2_size]
                subscriptions = result[0]
                self.extranonce1 = result[1]
                self.extranonce2_size = result[2] if len(result) > 2 else 4
                
                print(f"✅ Subscriptions: {subscriptions}")
                print(f"✅ Extranonce1: {self.extranonce1}")
                print(f"✅ Extranonce2 size: {self.extranonce2_size}")
                return True
        except Exception as e:
            print(f"❌ Error parsing subscribe response: {e}")
        return False
    
    def handle_authorize_response(self, result):
        """Handle mining.authorize response"""
        if result:
            print(f"✅ Worker '{self.username}' authorized on Antpool!")
        else:
            print(f"❌ Worker '{self.username}' authorization failed on Antpool!")
    
    def handle_mining_notify(self, params):
        """Handle mining.notify - new work from Antpool"""
        self.jobs_received += 1
        print(f"📢 NEW WORK from Antpool! (Job #{self.jobs_received})")
        
        if len(params) >= 8:
            job = {
                'job_id': params[0],
                'prevhash': params[1],
                'coinb1': params[2],
                'coinb2': params[3],
                'merkle_branch': params[4],
                'version': params[5],
                'nbits': params[6],
                'ntime': params[7],
                'clean_jobs': params[8] if len(params) > 8 else False
            }
            
            self.current_job = job
            print(f"Job ID: {job['job_id']}")
            print(f"Previous Hash: {job['prevhash'][:20]}...")
            print(f"Version: {job['version']}")
            print(f"nBits: {job['nbits']}")
            print(f"nTime: {job['ntime']}")
            print(f"Merkle branches: {len(job['merkle_branch'])}")
            print(f"Clean Jobs: {job['clean_jobs']}")
            
            # Start mining this job in background
            threading.Thread(target=self.mine_job, args=(job,), daemon=True).start()
        else:
            print("❌ Invalid mining.notify parameters from Antpool")
    
    def handle_set_difficulty(self, params):
        """Handle mining.set_difficulty from Antpool"""
        if params and len(params) > 0:
            old_difficulty = self.difficulty
            self.difficulty = params[0]
            print(f"🎯 Antpool difficulty changed: {old_difficulty} → {self.difficulty}")
    
    def handle_set_extranonce(self, params):
        """Handle mining.set_extranonce from Antpool"""
        if params and len(params) >= 2:
            self.extranonce1 = params[0]
            self.extranonce2_size = params[1]
            print(f"🔧 Antpool extranonce updated: {self.extranonce1}, size: {self.extranonce2_size}")
    
    def mine_job(self, job):
        """Mine a specific job (simplified demonstration)"""
        print(f"⛏️ Starting to mine Antpool job {job['job_id']}...")
        
        if not self.extranonce1:
            print("❌ No extranonce1 available for mining")
            return
        
        # Use a simple extranonce2
        extranonce2 = "00000001"
        if len(extranonce2) < self.extranonce2_size * 2:
            extranonce2 = extranonce2.ljust(self.extranonce2_size * 2, '0')
        
        # Build coinbase transaction
        coinbase = job['coinb1'] + self.extranonce1 + extranonce2 + job['coinb2']
        
        # Calculate merkle root
        merkle_root = self.calculate_merkle_root(coinbase, job['merkle_branch'])
        
        # Calculate target from difficulty
        target = 0x00000000FFFF0000000000000000000000000000000000000000000000000000 // self.difficulty
        
        print(f"🎯 Mining target (difficulty {self.difficulty}): {target:064x}")
        
        # Try some nonces (limited for demonstration)
        start_time = time.time()
        for nonce in range(100000):  # Limited range for demo
            if not self.running or self.current_job != job:
                print(f"⏹️ Stopping mining job {job['job_id']} (new job or stopped)")
                break
                
            # Build block header
            header = self.build_block_header(
                job['version'], job['prevhash'], merkle_root,
                job['ntime'], job['nbits'], nonce
            )
            
            # Hash the header (double SHA256)
            header_bin = binascii.unhexlify(header)
            hash_result = hashlib.sha256(hashlib.sha256(header_bin).digest()).digest()
            hash_int = int.from_bytes(hash_result, 'big')
            
            # Progress indicator
            if nonce % 10000 == 0:
                elapsed = time.time() - start_time
                hashrate = nonce / elapsed if elapsed > 0 else 0
                print(f"⚡ Nonce: {nonce:6d}, Hashrate: {hashrate:8.0f} H/s, Hash: {hash_result.hex()[:16]}...")
            
            # Check if we found a valid share
            if hash_int <= target:
                print(f"🎉 SHARE FOUND! Nonce: {nonce}, Hash: {hash_result.hex()}")
                self.submit_share(job['job_id'], extranonce2, job['ntime'], nonce)
                # Continue mining for more shares
            
        elapsed = time.time() - start_time
        avg_hashrate = 100000 / elapsed if elapsed > 0 else 0
        print(f"⛏️ Finished mining job {job['job_id']} - Avg hashrate: {avg_hashrate:.0f} H/s")
    
    def calculate_merkle_root(self, coinbase: str, merkle_branch: list) -> str:
        """Calculate merkle root from coinbase and merkle branch"""
        # Hash coinbase transaction (double SHA256)
        coinbase_bin = binascii.unhexlify(coinbase)
        current_hash = hashlib.sha256(hashlib.sha256(coinbase_bin).digest()).digest()
        
        # Apply merkle branch
        for branch_hash in merkle_branch:
            branch_bin = binascii.unhexlify(branch_hash)
            combined = current_hash + branch_bin
            current_hash = hashlib.sha256(hashlib.sha256(combined).digest()).digest()
        
        return current_hash.hex()
    
    def build_block_header(self, version: str, prevhash: str, merkle_root: str, 
                          ntime: str, nbits: str, nonce: int) -> str:
        """Build block header for hashing"""
        # Convert to little-endian format
        version_le = struct.pack("<I", int(version, 16)).hex()
        
        # Reverse prevhash (it's in big-endian, we need little-endian)
        prevhash_le = ''.join(reversed([prevhash[i:i+2] for i in range(0, len(prevhash), 2)]))
        
        # Reverse merkle root
        merkle_le = ''.join(reversed([merkle_root[i:i+2] for i in range(0, len(merkle_root), 2)]))
        
        ntime_le = struct.pack("<I", int(ntime, 16)).hex()
        nbits_le = struct.pack("<I", int(nbits, 16)).hex()
        nonce_le = struct.pack("<I", nonce).hex()
        
        header = version_le + prevhash_le + merkle_le + ntime_le + nbits_le + nonce_le
        return header
    
    def submit_share(self, job_id: str, extranonce2: str, ntime: str, nonce: int):
        """Submit a mining share to Antpool"""
        nonce_hex = f"{nonce:08x}"
        params = [self.username, job_id, extranonce2, ntime, nonce_hex]
        
        print(f"📤 Submitting share to Antpool: Job={job_id}, Nonce={nonce_hex}")
        if self.send_message("mining.submit", params):
            self.shares_submitted += 1
    
    def start_mining(self, duration: int = 120):
        """Start mining on Antpool"""
        print(f"🚀 Starting Antpool mining session")
        print(f"Pool: {self.host}:{self.port}")
        print(f"Worker: {self.username}")
        print("=" * 60)
        
        if not self.connect():
            return False
        
        self.running = True
        
        # Start message receiving thread
        receive_thread = threading.Thread(target=self.receive_messages)
        receive_thread.daemon = True
        receive_thread.start()
        
        # Step 1: Subscribe to mining
        print("\n🔔 Step 1: Subscribing to Antpool...")
        if not self.send_message("mining.subscribe", ["AntpoolMiner/1.0"]):
            return False
        time.sleep(3)
        
        # Step 2: Authorize worker
        print(f"\n🔐 Step 2: Authorizing worker {self.username}...")
        if not self.send_message("mining.authorize", [self.username, self.password]):
            return False
        time.sleep(3)
        
        # Step 3: Wait for mining work and mine
        print(f"\n⛏️ Step 3: Mining for {duration} seconds...")
        start_time = time.time()
        
        while self.running and (time.time() - start_time) < duration:
            time.sleep(1)
            
            # Print periodic status
            if int(time.time() - start_time) % 30 == 0:
                elapsed = time.time() - start_time
                print(f"\n📊 Status after {elapsed:.0f}s:")
                print(f"   Jobs received: {self.jobs_received}")
                print(f"   Shares submitted: {self.shares_submitted}")
                print(f"   Shares accepted: {self.shares_accepted}")
                print(f"   Shares rejected: {self.shares_rejected}")
                if self.current_job:
                    print(f"   Current job: {self.current_job['job_id']}")
        
        self.running = False
        if self.socket:
            self.socket.close()
        
        # Final statistics
        print(f"\n📊 Antpool Mining Session Complete!")
        print("=" * 50)
        print(f"Duration: {duration} seconds")
        print(f"Jobs received: {self.jobs_received}")
        print(f"Shares submitted: {self.shares_submitted}")
        print(f"Shares accepted: {self.shares_accepted}")
        print(f"Shares rejected: {self.shares_rejected}")
        if self.shares_submitted > 0:
            acceptance_rate = (self.shares_accepted / self.shares_submitted) * 100
            print(f"Acceptance rate: {acceptance_rate:.1f}%")
        
        return True

def main():
    print("🏊‍♂️ Antpool Real Mining Test")
    print("=" * 50)
    
    # Antpool connection details
    host = "ss.antpool.com"
    port = 3333
    username = "afshin01.miner"
    password = "123456"
    
    print(f"Testing Antpool with real credentials:")
    print(f"Host: {host}:{port}")
    print(f"Username: {username}")
    print(f"Password: {'*' * len(password)}")
    
    miner = AntpoolMiner(host, port, username, password)
    success = miner.start_mining(duration=90)
    
    if success:
        print("\n✅ Antpool test completed!")
    else:
        print("\n❌ Antpool test failed!")

if __name__ == "__main__":
    main()
