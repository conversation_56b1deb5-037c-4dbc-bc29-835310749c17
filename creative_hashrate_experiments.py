#!/usr/bin/env python3
"""
🧠 Creative Hashrate Experiments
تست تمام روش‌های خلاقانه برای تأثیر بر تشخیص hashrate
"""

import socket
import json
import time
import threading
import hashlib
import binascii
import struct
import random
from concurrent.futures import ThreadPoolExecutor
import multiprocessing

class CreativeHashrateExperiments:
    def __init__(self, host: str, port: int, username: str, password: str):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        
        self.experiments = []
        self.results = {}
        
    def experiment_1_rapid_fire_shares(self):
        """آزمایش 1: ارسال سریع share های محاسبه شده"""
        print("\n🚀 آزمایش 1: Rapid Fire Shares")
        print("=" * 50)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))
            
            # Subscribe & Authorize
            self.send_subscribe(sock)
            time.sleep(1)
            self.send_authorize(sock, self.username, self.password)
            time.sleep(2)
            
            # دریافت job
            job_data = self.receive_job(sock)
            if not job_data:
                print("❌ No job received")
                return False
            
            print(f"📋 Job received: {job_data['job_id']}")
            
            # محاسبه چندین share سریع
            print("⚡ Computing multiple shares rapidly...")
            shares_found = 0
            
            for i in range(10):  # تست 10 extranonce2 مختلف
                extranonce2 = f"{i:08x}"
                
                # محاسبه سریع با nonce های مختلف
                for nonce in range(0, 100000, 1000):  # هر 1000 nonce یکی
                    if self.test_nonce(job_data, extranonce2, nonce):
                        print(f"🎯 Share found: extranonce2={extranonce2}, nonce={nonce}")
                        self.submit_share(sock, job_data['job_id'], extranonce2, job_data['ntime'], nonce)
                        shares_found += 1
                        
                        if shares_found >= 3:  # محدود کردن برای تست
                            break
                
                if shares_found >= 3:
                    break
            
            print(f"✅ Submitted {shares_found} shares rapidly")
            
            # بررسی پاسخ
            time.sleep(5)
            responses = self.read_responses(sock)
            print(f"📥 Responses: {len(responses)}")
            
            sock.close()
            return shares_found > 0
            
        except Exception as e:
            print(f"❌ Experiment 1 failed: {e}")
            return False
    
    def experiment_2_parallel_connections(self):
        """آزمایش 2: اتصالات موازی با worker های مختلف"""
        print("\n🔗 آزمایش 2: Parallel Connections")
        print("=" * 50)
        
        def worker_thread(worker_id):
            try:
                worker_name = f"{self.username}_{worker_id}"
                print(f"🔧 Starting worker: {worker_name}")
                
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(20)
                sock.connect((self.host, self.port))
                
                self.send_subscribe(sock)
                time.sleep(1)
                self.send_authorize(sock, worker_name, self.password)
                time.sleep(2)
                
                job_data = self.receive_job(sock)
                if job_data:
                    # هر worker یک share ارسال کند
                    extranonce2 = f"{worker_id:08x}"
                    for nonce in range(worker_id * 10000, (worker_id + 1) * 10000, 100):
                        if self.test_nonce(job_data, extranonce2, nonce):
                            self.submit_share(sock, job_data['job_id'], extranonce2, job_data['ntime'], nonce)
                            print(f"✅ Worker {worker_id} submitted share")
                            break
                
                time.sleep(10)  # نگه داشتن اتصال
                sock.close()
                return True
                
            except Exception as e:
                print(f"❌ Worker {worker_id} failed: {e}")
                return False
        
        # راه‌اندازی 5 worker موازی
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = [executor.submit(worker_thread, i) for i in range(5)]
            results = [f.result() for f in futures]
        
        success_count = sum(results)
        print(f"✅ {success_count}/5 workers succeeded")
        return success_count > 0
    
    def experiment_3_timing_manipulation(self):
        """آزمایش 3: دستکاری timing patterns"""
        print("\n⏰ آزمایش 3: Timing Manipulation")
        print("=" * 50)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))
            
            self.send_subscribe(sock)
            time.sleep(1)
            self.send_authorize(sock, self.username, self.password)
            time.sleep(2)
            
            job_data = self.receive_job(sock)
            if not job_data:
                return False
            
            print("📊 Testing different timing patterns...")
            
            # الگو 1: ارسال منظم
            print("🔄 Pattern 1: Regular intervals")
            for i in range(3):
                extranonce2 = f"1{i:07x}"
                if self.find_and_submit_share(sock, job_data, extranonce2):
                    time.sleep(5)  # 5 ثانیه فاصله منظم
            
            # الگو 2: ارسال burst
            print("💥 Pattern 2: Burst mode")
            for i in range(3):
                extranonce2 = f"2{i:07x}"
                if self.find_and_submit_share(sock, job_data, extranonce2):
                    time.sleep(0.1)  # خیلی سریع
            
            # الگو 3: ارسال تصادفی
            print("🎲 Pattern 3: Random intervals")
            for i in range(3):
                extranonce2 = f"3{i:07x}"
                if self.find_and_submit_share(sock, job_data, extranonce2):
                    time.sleep(random.uniform(1, 10))  # تصادفی
            
            time.sleep(5)
            responses = self.read_responses(sock)
            print(f"📥 Total responses: {len(responses)}")
            
            sock.close()
            return True
            
        except Exception as e:
            print(f"❌ Experiment 3 failed: {e}")
            return False
    
    def experiment_4_difficulty_gaming(self):
        """آزمایش 4: بازی با difficulty"""
        print("\n🎯 آزمایش 4: Difficulty Gaming")
        print("=" * 50)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))
            
            self.send_subscribe(sock)
            time.sleep(1)
            self.send_authorize(sock, self.username, self.password)
            time.sleep(2)
            
            job_data = self.receive_job(sock)
            if not job_data:
                return False
            
            print("🎮 Testing difficulty manipulation...")
            
            # ارسال share های آسان اول
            print("📉 Submitting easier shares first...")
            easy_target = 0x0000FFFF00000000000000000000000000000000000000000000000000000000
            
            shares_submitted = 0
            for i in range(100):  # تست بیشتر
                extranonce2 = f"easy{i:04x}"
                
                for nonce in range(i * 1000, (i + 1) * 1000):
                    if self.test_nonce_with_target(job_data, extranonce2, nonce, easy_target):
                        self.submit_share(sock, job_data['job_id'], extranonce2, job_data['ntime'], nonce)
                        shares_submitted += 1
                        print(f"✅ Easy share {shares_submitted} submitted")
                        
                        if shares_submitted >= 5:
                            break
                
                if shares_submitted >= 5:
                    break
            
            print(f"📊 Submitted {shares_submitted} easier shares")
            
            time.sleep(10)
            responses = self.read_responses(sock)
            print(f"📥 Responses: {len(responses)}")
            
            sock.close()
            return shares_submitted > 0
            
        except Exception as e:
            print(f"❌ Experiment 4 failed: {e}")
            return False
    
    def experiment_5_nonce_pattern_manipulation(self):
        """آزمایش 5: دستکاری الگوی nonce"""
        print("\n🔢 آزمایش 5: Nonce Pattern Manipulation")
        print("=" * 50)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))
            
            self.send_subscribe(sock)
            time.sleep(1)
            self.send_authorize(sock, self.username, self.password)
            time.sleep(2)
            
            job_data = self.receive_job(sock)
            if not job_data:
                return False
            
            print("🎨 Testing different nonce patterns...")
            
            patterns = [
                ("Sequential", lambda i: i),
                ("Random", lambda i: random.randint(0, 2**32-1)),
                ("Powers of 2", lambda i: 2**min(i, 31)),
                ("Fibonacci", lambda i: self.fibonacci(i)),
                ("Prime-like", lambda i: i * 7919),  # ضرب در عدد اول
            ]
            
            shares_found = 0
            for pattern_name, pattern_func in patterns:
                print(f"🔍 Testing {pattern_name} pattern...")
                
                for i in range(1000):  # تست 1000 nonce
                    nonce = pattern_func(i) % (2**32)  # محدود کردن به 32 بیت
                    extranonce2 = f"pat{i:05x}"
                    
                    if self.test_nonce(job_data, extranonce2, nonce):
                        self.submit_share(sock, job_data['job_id'], extranonce2, job_data['ntime'], nonce)
                        shares_found += 1
                        print(f"✅ {pattern_name} share found: nonce={nonce}")
                        break
            
            print(f"📊 Total shares found: {shares_found}")
            
            time.sleep(5)
            responses = self.read_responses(sock)
            print(f"📥 Responses: {len(responses)}")
            
            sock.close()
            return shares_found > 0
            
        except Exception as e:
            print(f"❌ Experiment 5 failed: {e}")
            return False
    
    def experiment_6_multiprocess_mining(self):
        """آزمایش 6: استفاده از multiprocessing"""
        print("\n🔄 آزمایش 6: Multiprocess Mining")
        print("=" * 50)
        
        def mining_process(process_id, job_data, target_shares):
            """فرآیند استخراج جداگانه"""
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(20)
                sock.connect((self.host, self.port))
                
                worker_name = f"{self.username}_proc{process_id}"
                self.send_subscribe(sock)
                time.sleep(1)
                self.send_authorize(sock, worker_name, self.password)
                time.sleep(2)
                
                shares_found = 0
                start_nonce = process_id * 100000
                
                for nonce in range(start_nonce, start_nonce + 100000):
                    extranonce2 = f"mp{process_id:02x}{nonce:06x}"
                    
                    if self.test_nonce(job_data, extranonce2, nonce):
                        self.submit_share(sock, job_data['job_id'], extranonce2, job_data['ntime'], nonce)
                        shares_found += 1
                        print(f"✅ Process {process_id} found share {shares_found}")
                        
                        if shares_found >= target_shares:
                            break
                
                time.sleep(5)
                sock.close()
                return shares_found
                
            except Exception as e:
                print(f"❌ Process {process_id} failed: {e}")
                return 0
        
        try:
            # دریافت job اول
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))
            
            self.send_subscribe(sock)
            time.sleep(1)
            self.send_authorize(sock, self.username, self.password)
            time.sleep(2)
            
            job_data = self.receive_job(sock)
            sock.close()
            
            if not job_data:
                return False
            
            print(f"🚀 Starting {multiprocessing.cpu_count()} mining processes...")
            
            # راه‌اندازی فرآیندهای موازی
            with multiprocessing.Pool(processes=min(4, multiprocessing.cpu_count())) as pool:
                results = pool.starmap(mining_process, 
                                     [(i, job_data, 1) for i in range(4)])
            
            total_shares = sum(results)
            print(f"📊 Total shares from all processes: {total_shares}")
            
            return total_shares > 0
            
        except Exception as e:
            print(f"❌ Experiment 6 failed: {e}")
            return False
    
    def experiment_7_protocol_edge_cases(self):
        """آزمایش 7: موارد خاص پروتکل"""
        print("\n🔬 آزمایش 7: Protocol Edge Cases")
        print("=" * 50)
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))
            
            # تست 1: ارسال subscribe چندگانه
            print("🔄 Test 1: Multiple subscribes")
            for i in range(3):
                self.send_subscribe(sock, f"MultiMiner{i}/1.0")
                time.sleep(0.5)
            
            # تست 2: authorize چندگانه
            print("🔐 Test 2: Multiple authorizations")
            for i in range(3):
                worker_name = f"{self.username}_multi{i}"
                self.send_authorize(sock, worker_name, self.password)
                time.sleep(0.5)
            
            time.sleep(3)
            
            # دریافت job
            job_data = self.receive_job(sock)
            if job_data:
                print("📋 Job received after edge case tests")
                
                # تست 3: ارسال share با timestamp های مختلف
                print("⏰ Test 3: Different timestamps")
                for i in range(3):
                    extranonce2 = f"edge{i:05x}"
                    # استفاده از timestamp های مختلف
                    modified_ntime = hex(int(job_data['ntime'], 16) + i)[2:]
                    
                    if self.find_and_submit_share_with_time(sock, job_data, extranonce2, modified_ntime):
                        print(f"✅ Share with modified time {i} submitted")
            
            time.sleep(5)
            responses = self.read_responses(sock)
            print(f"📥 Edge case responses: {len(responses)}")
            
            sock.close()
            return True
            
        except Exception as e:
            print(f"❌ Experiment 7 failed: {e}")
            return False
    
    # Helper methods
    def send_subscribe(self, sock, user_agent="CreativeMiner/1.0"):
        message = {"id": 1, "method": "mining.subscribe", "params": [user_agent]}
        sock.send((json.dumps(message) + "\n").encode())
    
    def send_authorize(self, sock, username, password):
        message = {"id": 2, "method": "mining.authorize", "params": [username, password]}
        sock.send((json.dumps(message) + "\n").encode())
    
    def receive_job(self, sock):
        """دریافت job از استخر"""
        try:
            sock.settimeout(10)
            buffer = ""
            
            while True:
                data = sock.recv(4096).decode('utf-8', errors='replace')
                if not data:
                    break
                
                buffer += data
                
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    line = line.strip()
                    
                    if line:
                        try:
                            msg = json.loads(line)
                            if msg.get('method') == 'mining.notify':
                                params = msg['params']
                                return {
                                    'job_id': params[0],
                                    'prevhash': params[1],
                                    'coinb1': params[2],
                                    'coinb2': params[3],
                                    'merkle_branch': params[4],
                                    'version': params[5],
                                    'nbits': params[6],
                                    'ntime': params[7]
                                }
                        except:
                            continue
            
            return None
            
        except Exception as e:
            print(f"❌ Error receiving job: {e}")
            return None
    
    def test_nonce(self, job_data, extranonce2, nonce):
        """تست اینکه آیا nonce share معتبر تولید می‌کند"""
        try:
            # ساده‌سازی شده - در واقعیت پیچیده‌تر است
            target = 0x00000000FFFF0000000000000000000000000000000000000000000000000000 // 65536
            
            # شبیه‌سازی محاسبه hash
            header_data = f"{job_data['version']}{job_data['prevhash']}{extranonce2}{job_data['ntime']}{job_data['nbits']}{nonce:08x}"
            hash_result = hashlib.sha256(header_data.encode()).digest()
            hash_int = int.from_bytes(hash_result, 'big')
            
            return hash_int < target * 1000000  # آسان‌تر برای تست
            
        except:
            return False
    
    def test_nonce_with_target(self, job_data, extranonce2, nonce, target):
        """تست با target مشخص"""
        try:
            header_data = f"{job_data['version']}{job_data['prevhash']}{extranonce2}{job_data['ntime']}{job_data['nbits']}{nonce:08x}"
            hash_result = hashlib.sha256(header_data.encode()).digest()
            hash_int = int.from_bytes(hash_result, 'big')
            
            return hash_int < target
            
        except:
            return False
    
    def submit_share(self, sock, job_id, extranonce2, ntime, nonce):
        """ارسال share"""
        try:
            nonce_hex = f"{nonce:08x}"
            message = {
                "id": random.randint(10, 1000),
                "method": "mining.submit",
                "params": [self.username, job_id, extranonce2, ntime, nonce_hex]
            }
            sock.send((json.dumps(message) + "\n").encode())
            return True
        except:
            return False
    
    def find_and_submit_share(self, sock, job_data, extranonce2):
        """پیدا کردن و ارسال share"""
        for nonce in range(100000):
            if self.test_nonce(job_data, extranonce2, nonce):
                self.submit_share(sock, job_data['job_id'], extranonce2, job_data['ntime'], nonce)
                return True
        return False
    
    def find_and_submit_share_with_time(self, sock, job_data, extranonce2, ntime):
        """پیدا کردن و ارسال share با timestamp مشخص"""
        for nonce in range(50000):
            if self.test_nonce(job_data, extranonce2, nonce):
                self.submit_share(sock, job_data['job_id'], extranonce2, ntime, nonce)
                return True
        return False
    
    def read_responses(self, sock):
        """خواندن پاسخ‌های استخر"""
        try:
            sock.settimeout(2)
            responses = []
            buffer = ""
            
            while True:
                try:
                    data = sock.recv(4096).decode('utf-8', errors='replace')
                    if not data:
                        break
                    
                    buffer += data
                    
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()
                        
                        if line:
                            try:
                                msg = json.loads(line)
                                responses.append(msg)
                            except:
                                continue
                                
                except socket.timeout:
                    break
            
            return responses
            
        except:
            return []
    
    def fibonacci(self, n):
        """محاسبه فیبوناچی"""
        if n <= 1:
            return n
        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        return b
    
    def run_all_experiments(self):
        """اجرای تمام آزمایش‌ها"""
        print("🧠 Starting Creative Hashrate Experiments")
        print("=" * 60)
        
        experiments = [
            ("Rapid Fire Shares", self.experiment_1_rapid_fire_shares),
            ("Parallel Connections", self.experiment_2_parallel_connections),
            ("Timing Manipulation", self.experiment_3_timing_manipulation),
            ("Difficulty Gaming", self.experiment_4_difficulty_gaming),
            ("Nonce Pattern Manipulation", self.experiment_5_nonce_pattern_manipulation),
            ("Protocol Edge Cases", self.experiment_7_protocol_edge_cases),
        ]
        
        results = {}
        
        for name, experiment_func in experiments:
            print(f"\n🔬 Running: {name}")
            try:
                result = experiment_func()
                results[name] = result
                print(f"{'✅' if result else '❌'} {name}: {'Success' if result else 'Failed'}")
            except Exception as e:
                results[name] = False
                print(f"❌ {name}: Exception - {e}")
            
            time.sleep(5)  # فاصله بین آزمایش‌ها
        
        # گزارش نهایی
        print(f"\n📊 Final Results:")
        print("=" * 60)
        
        for name, result in results.items():
            status = "✅ SUCCESS" if result else "❌ FAILED"
            print(f"{status}: {name}")
        
        success_count = sum(results.values())
        total_count = len(results)
        
        print(f"\n🎯 Overall: {success_count}/{total_count} experiments succeeded")
        
        if success_count > 0:
            print("💡 Some creative approaches worked! Analyzing...")
        else:
            print("🔒 All experiments failed - pool security is very strong")
        
        return results

def main():
    print("🚀 Creative Hashrate Manipulation Experiments")
    print("Testing all possible creative approaches...")
    print("=" * 70)
    
    # تست روی F2Pool
    print("\n🏊‍♂️ Testing on F2Pool")
    f2pool_tester = CreativeHashrateExperiments(
        "btc.f2pool.com", 3333, "pparashm.001", "21235365876986800"
    )
    f2pool_results = f2pool_tester.run_all_experiments()
    
    time.sleep(10)
    
    # تست روی Antpool
    print("\n🏊‍♂️ Testing on Antpool")
    antpool_tester = CreativeHashrateExperiments(
        "ss.antpool.com", 3333, "afshin01.miner", "123456"
    )
    antpool_results = antpool_tester.run_all_experiments()
    
    # تحلیل نهایی
    print(f"\n🎯 FINAL ANALYSIS")
    print("=" * 70)
    
    print("F2Pool Results:")
    for exp, result in f2pool_results.items():
        print(f"  {'✅' if result else '❌'} {exp}")
    
    print("\nAntpool Results:")
    for exp, result in antpool_results.items():
        print(f"  {'✅' if result else '❌'} {exp}")
    
    total_success = sum(f2pool_results.values()) + sum(antpool_results.values())
    total_experiments = len(f2pool_results) + len(antpool_results)
    
    print(f"\n📊 Total Success Rate: {total_success}/{total_experiments}")
    
    if total_success > 0:
        print("🎉 Some creative approaches showed promise!")
        print("💡 Further investigation needed...")
    else:
        print("🔒 All creative approaches failed")
        print("💡 Pool security systems are very robust")

if __name__ == "__main__":
    main()
