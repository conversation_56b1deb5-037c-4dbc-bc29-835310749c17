#!/usr/bin/env python3
"""
🔬 Advanced Pool Analysis
تحلیل عمیق رفتار استخرها و یافتن نقاط ضعف
"""

import socket
import json
import time
import threading
import hashlib
import binascii
import struct
import random
from datetime import datetime

class AdvancedPoolAnalyzer:
    def __init__(self, host: str, port: int, username: str, password: str):
        self.host = host
        self.port = port
        self.username = username
        self.password = password
        
        self.analysis_data = {
            'response_times': [],
            'difficulty_changes': [],
            'job_intervals': [],
            'share_responses': [],
            'connection_behavior': []
        }
    
    def analyze_pool_behavior(self):
        """تحلیل رفتار کلی استخر"""
        print(f"\n🔍 Analyzing {self.host} behavior...")
        print("=" * 50)
        
        # آزمایش 1: تحلیل زمان پاسخ
        self.test_response_times()
        
        # آزمایش 2: تحلیل الگوی job
        self.test_job_patterns()
        
        # آزمایش 3: تحلیل share validation
        self.test_share_validation()
        
        # آزمایش 4: تحلیل اتصالات متعدد
        self.test_multiple_connections()
        
        # تحلیل نتایج
        self.analyze_results()
    
    def test_response_times(self):
        """تست زمان‌های پاسخ"""
        print("⏱️ Testing response times...")
        
        for i in range(5):
            try:
                start_time = time.time()
                
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(10)
                sock.connect((self.host, self.port))
                
                connect_time = time.time() - start_time
                
                # ارسال subscribe
                subscribe_start = time.time()
                self.send_message(sock, "mining.subscribe", ["ResponseTester/1.0"])
                
                # انتظار پاسخ
                response = self.wait_for_response(sock, 1)
                subscribe_time = time.time() - subscribe_start
                
                # ارسال authorize
                auth_start = time.time()
                self.send_message(sock, "mining.authorize", [f"{self.username}_test{i}", self.password])
                
                auth_response = self.wait_for_response(sock, 2)
                auth_time = time.time() - auth_start
                
                self.analysis_data['response_times'].append({
                    'connect_time': connect_time,
                    'subscribe_time': subscribe_time,
                    'auth_time': auth_time,
                    'subscribe_success': response is not None,
                    'auth_success': auth_response is not None
                })
                
                print(f"  Test {i+1}: Connect={connect_time:.3f}s, Subscribe={subscribe_time:.3f}s, Auth={auth_time:.3f}s")
                
                sock.close()
                time.sleep(1)
                
            except Exception as e:
                print(f"  Test {i+1} failed: {e}")
    
    def test_job_patterns(self):
        """تحلیل الگوی ارسال job"""
        print("📋 Analyzing job patterns...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))
            
            self.send_message(sock, "mining.subscribe", ["JobAnalyzer/1.0"])
            time.sleep(1)
            self.send_message(sock, "mining.authorize", [f"{self.username}_jobtest", self.password])
            time.sleep(2)
            
            jobs_received = []
            start_time = time.time()
            
            # مانیتور کردن job ها برای 60 ثانیه
            while time.time() - start_time < 60:
                try:
                    sock.settimeout(5)
                    data = sock.recv(4096).decode('utf-8', errors='replace')
                    
                    if data:
                        for line in data.strip().split('\n'):
                            if line:
                                try:
                                    msg = json.loads(line)
                                    if msg.get('method') == 'mining.notify':
                                        job_time = time.time()
                                        jobs_received.append({
                                            'time': job_time,
                                            'job_id': msg['params'][0],
                                            'clean_jobs': msg['params'][8] if len(msg['params']) > 8 else False
                                        })
                                        print(f"  Job received: {msg['params'][0]} at {job_time:.3f}")
                                except:
                                    continue
                
                except socket.timeout:
                    continue
            
            # تحلیل فواصل زمانی
            if len(jobs_received) > 1:
                intervals = []
                for i in range(1, len(jobs_received)):
                    interval = jobs_received[i]['time'] - jobs_received[i-1]['time']
                    intervals.append(interval)
                
                self.analysis_data['job_intervals'] = intervals
                avg_interval = sum(intervals) / len(intervals)
                print(f"  Average job interval: {avg_interval:.2f} seconds")
                print(f"  Total jobs received: {len(jobs_received)}")
            
            sock.close()
            
        except Exception as e:
            print(f"  Job pattern analysis failed: {e}")
    
    def test_share_validation(self):
        """تست validation share ها"""
        print("🎯 Testing share validation...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(30)
            sock.connect((self.host, self.port))
            
            self.send_message(sock, "mining.subscribe", ["ShareTester/1.0"])
            time.sleep(1)
            self.send_message(sock, "mining.authorize", [f"{self.username}_sharetest", self.password])
            time.sleep(2)
            
            # دریافت job
            job_data = self.get_job(sock)
            if not job_data:
                print("  No job received for share testing")
                return
            
            print(f"  Testing with job: {job_data['job_id']}")
            
            # تست انواع مختلف share
            test_cases = [
                ("Valid easy share", self.create_easy_share),
                ("Invalid nonce", self.create_invalid_share),
                ("Old job share", self.create_old_job_share),
                ("Modified timestamp", self.create_modified_time_share),
                ("Random data", self.create_random_share)
            ]
            
            for test_name, share_creator in test_cases:
                try:
                    share_data = share_creator(job_data)
                    if share_data:
                        submit_time = time.time()
                        self.submit_test_share(sock, share_data)
                        
                        # انتظار پاسخ
                        response = self.wait_for_share_response(sock, 5)
                        response_time = time.time() - submit_time
                        
                        self.analysis_data['share_responses'].append({
                            'test_name': test_name,
                            'response_time': response_time,
                            'accepted': response.get('result', False) if response else False,
                            'error': response.get('error') if response else None
                        })
                        
                        status = "✅" if (response and response.get('result')) else "❌"
                        print(f"  {status} {test_name}: {response_time:.3f}s")
                        
                        time.sleep(1)
                
                except Exception as e:
                    print(f"  ❌ {test_name} failed: {e}")
            
            sock.close()
            
        except Exception as e:
            print(f"  Share validation test failed: {e}")
    
    def test_multiple_connections(self):
        """تست اتصالات متعدد"""
        print("🔗 Testing multiple connections...")
        
        def connection_worker(worker_id):
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(20)
                
                connect_start = time.time()
                sock.connect((self.host, self.port))
                connect_time = time.time() - connect_start
                
                worker_name = f"{self.username}_multi{worker_id}"
                
                # Subscribe
                self.send_message(sock, "mining.subscribe", [f"MultiTester{worker_id}/1.0"])
                time.sleep(0.5)
                
                # Authorize
                self.send_message(sock, "mining.authorize", [worker_name, self.password])
                time.sleep(1)
                
                # نگه داشتن اتصال
                time.sleep(10)
                
                sock.close()
                
                return {
                    'worker_id': worker_id,
                    'connect_time': connect_time,
                    'success': True
                }
                
            except Exception as e:
                return {
                    'worker_id': worker_id,
                    'connect_time': None,
                    'success': False,
                    'error': str(e)
                }
        
        # راه‌اندازی 10 اتصال همزمان
        import concurrent.futures
        
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(connection_worker, i) for i in range(10)]
            results = [f.result() for f in futures]
        
        successful_connections = [r for r in results if r['success']]
        failed_connections = [r for r in results if not r['success']]
        
        self.analysis_data['connection_behavior'] = {
            'total_attempted': len(results),
            'successful': len(successful_connections),
            'failed': len(failed_connections),
            'success_rate': len(successful_connections) / len(results),
            'avg_connect_time': sum(r['connect_time'] for r in successful_connections if r['connect_time']) / max(len(successful_connections), 1)
        }
        
        print(f"  Successful connections: {len(successful_connections)}/10")
        print(f"  Success rate: {len(successful_connections)/10*100:.1f}%")
        
        if failed_connections:
            print("  Failed connections:")
            for conn in failed_connections[:3]:  # نمایش 3 خطای اول
                print(f"    Worker {conn['worker_id']}: {conn.get('error', 'Unknown error')}")
    
    def analyze_results(self):
        """تحلیل نتایج کلی"""
        print(f"\n📊 Analysis Results for {self.host}")
        print("=" * 50)
        
        # تحلیل زمان پاسخ
        if self.analysis_data['response_times']:
            response_times = self.analysis_data['response_times']
            avg_connect = sum(r['connect_time'] for r in response_times) / len(response_times)
            avg_subscribe = sum(r['subscribe_time'] for r in response_times if r['subscribe_success']) / max(sum(r['subscribe_success'] for r in response_times), 1)
            
            print(f"⏱️ Response Times:")
            print(f"  Average connect time: {avg_connect:.3f}s")
            print(f"  Average subscribe time: {avg_subscribe:.3f}s")
        
        # تحلیل job intervals
        if self.analysis_data['job_intervals']:
            intervals = self.analysis_data['job_intervals']
            avg_interval = sum(intervals) / len(intervals)
            min_interval = min(intervals)
            max_interval = max(intervals)
            
            print(f"📋 Job Patterns:")
            print(f"  Average interval: {avg_interval:.2f}s")
            print(f"  Min interval: {min_interval:.2f}s")
            print(f"  Max interval: {max_interval:.2f}s")
        
        # تحلیل share responses
        if self.analysis_data['share_responses']:
            shares = self.analysis_data['share_responses']
            accepted_shares = [s for s in shares if s['accepted']]
            
            print(f"🎯 Share Validation:")
            print(f"  Total shares tested: {len(shares)}")
            print(f"  Accepted shares: {len(accepted_shares)}")
            
            for share in shares:
                status = "✅" if share['accepted'] else "❌"
                print(f"  {status} {share['test_name']}: {share['response_time']:.3f}s")
        
        # تحلیل اتصالات
        if self.analysis_data['connection_behavior']:
            conn_data = self.analysis_data['connection_behavior']
            
            print(f"🔗 Connection Behavior:")
            print(f"  Success rate: {conn_data['success_rate']*100:.1f}%")
            print(f"  Average connect time: {conn_data['avg_connect_time']:.3f}s")
        
        # نتیجه‌گیری
        self.draw_conclusions()
    
    def draw_conclusions(self):
        """نتیجه‌گیری از تحلیل‌ها"""
        print(f"\n💡 Conclusions for {self.host}:")
        print("-" * 30)
        
        # بررسی نقاط ضعف احتمالی
        potential_weaknesses = []
        
        # چک کردن زمان پاسخ
        if self.analysis_data['response_times']:
            avg_times = [r['connect_time'] + r['subscribe_time'] + r['auth_time'] 
                        for r in self.analysis_data['response_times']]
            if avg_times and sum(avg_times)/len(avg_times) > 2.0:
                potential_weaknesses.append("Slow response times - possible overload")
        
        # چک کردن job intervals
        if self.analysis_data['job_intervals']:
            intervals = self.analysis_data['job_intervals']
            if len(set(intervals)) == 1:  # همه intervals یکسان
                potential_weaknesses.append("Fixed job intervals - predictable pattern")
        
        # چک کردن share validation
        if self.analysis_data['share_responses']:
            invalid_accepted = [s for s in self.analysis_data['share_responses'] 
                              if s['accepted'] and 'invalid' in s['test_name'].lower()]
            if invalid_accepted:
                potential_weaknesses.append("Accepts some invalid shares - weak validation")
        
        # چک کردن multiple connections
        if self.analysis_data['connection_behavior']:
            success_rate = self.analysis_data['connection_behavior']['success_rate']
            if success_rate == 1.0:
                potential_weaknesses.append("Allows unlimited connections - no rate limiting")
        
        if potential_weaknesses:
            print("⚠️ Potential weaknesses found:")
            for weakness in potential_weaknesses:
                print(f"  • {weakness}")
        else:
            print("🔒 No obvious weaknesses detected")
            print("  Pool appears to have strong security measures")
    
    # Helper methods
    def send_message(self, sock, method, params):
        message = {"id": random.randint(1, 1000), "method": method, "params": params}
        sock.send((json.dumps(message) + "\n").encode())
    
    def wait_for_response(self, sock, expected_id, timeout=5):
        try:
            sock.settimeout(timeout)
            buffer = ""
            
            while True:
                data = sock.recv(4096).decode('utf-8', errors='replace')
                if not data:
                    break
                
                buffer += data
                
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    if line.strip():
                        try:
                            msg = json.loads(line.strip())
                            if msg.get('id') == expected_id:
                                return msg
                        except:
                            continue
            
            return None
            
        except:
            return None
    
    def wait_for_share_response(self, sock, timeout=5):
        try:
            sock.settimeout(timeout)
            buffer = ""
            
            while True:
                data = sock.recv(4096).decode('utf-8', errors='replace')
                if not data:
                    break
                
                buffer += data
                
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    if line.strip():
                        try:
                            msg = json.loads(line.strip())
                            if 'result' in msg or 'error' in msg:
                                return msg
                        except:
                            continue
            
            return None
            
        except:
            return None
    
    def get_job(self, sock, timeout=10):
        try:
            sock.settimeout(timeout)
            buffer = ""
            
            while True:
                data = sock.recv(4096).decode('utf-8', errors='replace')
                if not data:
                    break
                
                buffer += data
                
                while '\n' in buffer:
                    line, buffer = buffer.split('\n', 1)
                    if line.strip():
                        try:
                            msg = json.loads(line.strip())
                            if msg.get('method') == 'mining.notify':
                                params = msg['params']
                                return {
                                    'job_id': params[0],
                                    'prevhash': params[1],
                                    'coinb1': params[2],
                                    'coinb2': params[3],
                                    'merkle_branch': params[4],
                                    'version': params[5],
                                    'nbits': params[6],
                                    'ntime': params[7]
                                }
                        except:
                            continue
            
            return None
            
        except:
            return None
    
    def create_easy_share(self, job_data):
        # ساخت share آسان برای تست
        return {
            'job_id': job_data['job_id'],
            'extranonce2': '00000001',
            'ntime': job_data['ntime'],
            'nonce': '12345678'
        }
    
    def create_invalid_share(self, job_data):
        return {
            'job_id': job_data['job_id'],
            'extranonce2': '00000002',
            'ntime': job_data['ntime'],
            'nonce': 'ffffffff'  # nonce نامعتبر
        }
    
    def create_old_job_share(self, job_data):
        return {
            'job_id': 'old_job_123',  # job قدیمی
            'extranonce2': '00000003',
            'ntime': job_data['ntime'],
            'nonce': '87654321'
        }
    
    def create_modified_time_share(self, job_data):
        # تغییر timestamp
        modified_time = hex(int(job_data['ntime'], 16) + 3600)[2:]  # +1 ساعت
        return {
            'job_id': job_data['job_id'],
            'extranonce2': '00000004',
            'ntime': modified_time,
            'nonce': '11111111'
        }
    
    def create_random_share(self, job_data):
        return {
            'job_id': job_data['job_id'],
            'extranonce2': f"{random.randint(0, 0xffffffff):08x}",
            'ntime': job_data['ntime'],
            'nonce': f"{random.randint(0, 0xffffffff):08x}"
        }
    
    def submit_test_share(self, sock, share_data):
        message = {
            "id": random.randint(100, 999),
            "method": "mining.submit",
            "params": [
                f"{self.username}_test",
                share_data['job_id'],
                share_data['extranonce2'],
                share_data['ntime'],
                share_data['nonce']
            ]
        }
        sock.send((json.dumps(message) + "\n").encode())

def main():
    print("🔬 Advanced Pool Security Analysis")
    print("=" * 60)
    
    pools = [
        ("btc.f2pool.com", 3333, "pparashm.001", "21235365876986800"),
        ("ss.antpool.com", 3333, "afshin01.miner", "123456")
    ]
    
    for host, port, username, password in pools:
        print(f"\n🏊‍♂️ Analyzing {host}:{port}")
        
        analyzer = AdvancedPoolAnalyzer(host, port, username, password)
        analyzer.analyze_pool_behavior()
        
        time.sleep(10)  # فاصله بین تحلیل استخرها
    
    print(f"\n🎯 Advanced Analysis Complete!")
    print("Check the detailed results above for potential security insights.")

if __name__ == "__main__":
    main()
