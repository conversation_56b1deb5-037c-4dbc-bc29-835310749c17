#!/usr/bin/env python3
"""
Stratum Protocol Tester for Bitcoin Mining Pools
Tests connection to F2Pool and other Stratum servers
"""

import socket
import json
import time
import sys
from typing import Optional, Dict, Any

class StratumTester:
    def __init__(self, host: str, port: int, timeout: int = 10):
        self.host = host
        self.port = port
        self.timeout = timeout
        self.socket = None
        
    def connect(self) -> bool:
        """Establish connection to the Stratum server"""
        try:
            print(f"Connecting to {self.host}:{self.port}...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            self.socket.connect((self.host, self.port))
            print("✓ Connection established!")
            return True
        except Exception as e:
            print(f"✗ Connection failed: {e}")
            return False
    
    def send_message(self, method: str, params: list = None, id: int = 1) -> Optional[Dict[Any, Any]]:
        """Send a JSON-RPC message to the Stratum server"""
        if not self.socket:
            print("✗ No connection established")
            return None
            
        message = {
            "id": id,
            "method": method,
            "params": params or []
        }
        
        try:
            # Send message
            json_message = json.dumps(message) + "\n"
            print(f"Sending: {json_message.strip()}")
            self.socket.send(json_message.encode('utf-8'))
            
            # Receive response
            response = self.socket.recv(4096).decode('utf-8').strip()
            print(f"Received: {response}")
            
            # Parse JSON response
            if response:
                return json.loads(response)
            return None
            
        except Exception as e:
            print(f"✗ Error sending/receiving message: {e}")
            return None
    
    def test_mining_subscribe(self) -> Optional[Dict[Any, Any]]:
        """Test mining.subscribe method"""
        print("\n--- Testing mining.subscribe ---")
        return self.send_message("mining.subscribe", ["miner02/1.0.0"])
    
    def test_mining_authorize(self, username: str = "test", password: str = "x") -> Optional[Dict[Any, Any]]:
        """Test mining.authorize method"""
        print("\n--- Testing mining.authorize ---")
        return self.send_message("mining.authorize", [username, password])
    
    def close(self):
        """Close the connection"""
        if self.socket:
            self.socket.close()
            print("Connection closed.")

def test_f2pool():
    """Test F2Pool Stratum servers"""
    # F2Pool actual Stratum ports for Bitcoin
    servers = [
        ("stratum.f2pool.com", 1314),
        ("stratum.f2pool.com", 3333),
        ("stratum.f2pool.com", 25),
        ("btc.f2pool.com", 1314),
        ("btc.f2pool.com", 3333),
        ("btc.f2pool.com", 25)
    ]
    
    for host, port in servers:
        print(f"\n{'='*50}")
        print(f"Testing {host}:{port}")
        print(f"{'='*50}")
        
        tester = StratumTester(host, port)
        
        if tester.connect():
            # Test mining.subscribe
            subscribe_response = tester.test_mining_subscribe()
            
            if subscribe_response:
                print(f"Subscribe response: {json.dumps(subscribe_response, indent=2)}")
                
                # Test mining.authorize with dummy credentials
                auth_response = tester.test_mining_authorize()
                if auth_response:
                    print(f"Authorize response: {json.dumps(auth_response, indent=2)}")
            
            # Wait a bit to see if server sends any additional messages
            print("\nWaiting for additional server messages...")
            try:
                tester.socket.settimeout(3)
                additional_data = tester.socket.recv(4096).decode('utf-8').strip()
                if additional_data:
                    print(f"Additional data received: {additional_data}")
            except socket.timeout:
                print("No additional messages received.")
            except Exception as e:
                print(f"Error receiving additional data: {e}")
        
        tester.close()
        time.sleep(1)

def main():
    print("Stratum Protocol Tester for Bitcoin Mining Pools")
    print("Testing F2Pool servers...")
    
    try:
        test_f2pool()
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()
