#!/usr/bin/env python3
"""
Antpool Stratum Tester
Tests connection to Antpool mining server
"""

import socket
import json
import time
import threading
import hashlib
import struct

class AntpoolTester:
    def __init__(self, host: str, port: int, timeout: int = 15):
        self.host = host
        self.port = port
        self.timeout = timeout
        self.socket = None
        self.running = False
        self.message_id = 1
        
    def connect(self) -> bool:
        """Connect to Antpool"""
        try:
            print(f"Connecting to {self.host}:{self.port}...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(self.timeout)
            self.socket.connect((self.host, self.port))
            print("✓ Connection established!")
            return True
        except Exception as e:
            print(f"✗ Connection failed: {e}")
            return False
    
    def listen_for_data(self):
        """Listen for incoming data"""
        buffer = ""
        while self.running and self.socket:
            try:
                self.socket.settimeout(1)
                data = self.socket.recv(4096)
                if data:
                    decoded = data.decode('utf-8', errors='replace')
                    buffer += decoded
                    
                    # Process complete JSON messages
                    while '\n' in buffer:
                        line, buffer = buffer.split('\n', 1)
                        line = line.strip()
                        if line:
                            self.process_message(line)
                else:
                    print("Connection closed by server")
                    break
            except socket.timeout:
                continue
            except Exception as e:
                print(f"Error receiving data: {e}")
                break
    
    def process_message(self, message: str):
        """Process received JSON message"""
        try:
            data = json.loads(message)
            print(f"[RECEIVED] {json.dumps(data, indent=2)}")
            
            # Handle different message types
            if 'method' in data:
                method = data['method']
                if method == 'mining.notify':
                    print("📢 New work notification received!")
                elif method == 'mining.set_difficulty':
                    print(f"🎯 Difficulty set to: {data.get('params', [None])[0]}")
                elif method == 'mining.set_extranonce':
                    print("🔧 Extranonce update received")
            elif 'result' in data:
                print(f"✅ Response to request {data.get('id')}: {data['result']}")
            elif 'error' in data:
                print(f"❌ Error response: {data['error']}")
                
        except json.JSONDecodeError:
            print(f"[RECEIVED] Non-JSON data: {message}")
        except Exception as e:
            print(f"Error processing message: {e}")
        
        print("-" * 50)
    
    def send_message(self, method: str, params: list = None, id: int = None):
        """Send Stratum message"""
        if id is None:
            id = self.message_id
            self.message_id += 1
            
        message = {
            "id": id,
            "method": method,
            "params": params or []
        }
        json_message = json.dumps(message) + "\n"
        
        try:
            print(f"[SENDING] {json_message.strip()}")
            self.socket.send(json_message.encode('utf-8'))
            return True
        except Exception as e:
            print(f"Error sending message: {e}")
            return False
    
    def test_mining_flow(self, duration: int = 30):
        """Test complete mining flow"""
        if not self.connect():
            return False
        
        self.running = True
        
        # Start listening thread
        listen_thread = threading.Thread(target=self.listen_for_data)
        listen_thread.daemon = True
        listen_thread.start()
        
        # Step 1: Subscribe to mining
        print("\n🔌 Step 1: Subscribing to mining...")
        self.send_message("mining.subscribe", ["BFGMiner/5.5.0", None, "ss.antpool.com", 3333])
        time.sleep(3)
        
        # Step 2: Authorize worker (using dummy credentials)
        print("\n🔐 Step 2: Authorizing worker...")
        self.send_message("mining.authorize", ["test_user.worker1", "123456"])
        time.sleep(3)
        
        # Step 3: Wait for mining.notify and other messages
        print(f"\n⏳ Step 3: Waiting {duration-6} seconds for mining notifications...")
        time.sleep(duration-6)
        
        self.running = False
        self.close()
        return True
    
    def close(self):
        """Close connection"""
        self.running = False
        if self.socket:
            self.socket.close()
            print("Connection closed.")

def test_antpool():
    """Test Antpool connection"""
    print("🏊‍♂️ Testing Antpool Stratum Connection")
    print("=" * 60)
    
    tester = AntpoolTester("ss.antpool.com", 3333)
    success = tester.test_mining_flow(duration=25)
    
    if success:
        print("\n✅ Antpool test completed successfully!")
    else:
        print("\n❌ Antpool test failed!")

def main():
    print("Antpool Stratum Protocol Tester")
    print("Testing: stratum+tcp://ss.antpool.com:3333")
    
    try:
        test_antpool()
    except KeyboardInterrupt:
        print("\nTest interrupted by user.")
    except Exception as e:
        print(f"Unexpected error: {e}")

if __name__ == "__main__":
    main()
