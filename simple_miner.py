#!/usr/bin/env python3
"""
Simple Bitcoin Stratum Miner
Based on BFGMiner concepts but simplified for educational purposes
"""

import socket
import json
import time
import threading
import hashlib
import struct
import binascii
from typing import Optional, Dict, Any

class StratumMiner:
    def __init__(self, pool_host: str, pool_port: int, username: str, password: str = "x"):
        self.pool_host = pool_host
        self.pool_port = pool_port
        self.username = username
        self.password = password
        
        self.socket = None
        self.running = False
        self.message_id = 1
        
        # Mining data
        self.extranonce1 = None
        self.extranonce2_size = None
        self.difficulty = 1
        self.job_id = None
        self.prevhash = None
        self.coinb1 = None
        self.coinb2 = None
        self.merkle_branch = []
        self.version = None
        self.nbits = None
        self.ntime = None
        
        # Statistics
        self.shares_submitted = 0
        self.shares_accepted = 0
        self.shares_rejected = 0
        
    def connect(self) -> bool:
        """Connect to mining pool"""
        try:
            print(f"🔌 Connecting to {self.pool_host}:{self.pool_port}...")
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(30)
            self.socket.connect((self.pool_host, self.pool_port))
            print("✅ Connected successfully!")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def send_message(self, method: str, params: list = None) -> bool:
        """Send JSON-RPC message to pool"""
        message = {
            "id": self.message_id,
            "method": method,
            "params": params or []
        }
        self.message_id += 1
        
        try:
            json_str = json.dumps(message) + "\n"
            print(f"📤 SEND: {json_str.strip()}")
            self.socket.send(json_str.encode('utf-8'))
            return True
        except Exception as e:
            print(f"❌ Send error: {e}")
            return False
    
    def receive_message(self) -> Optional[Dict[Any, Any]]:
        """Receive and parse JSON-RPC message"""
        try:
            data = self.socket.recv(4096).decode('utf-8').strip()
            if not data:
                return None
            
            print(f"📥 RECV: {data}")
            
            # Handle multiple JSON objects in one message
            for line in data.split('\n'):
                line = line.strip()
                if line:
                    try:
                        return json.loads(line)
                    except json.JSONDecodeError:
                        print(f"⚠️ Invalid JSON: {line}")
                        continue
            return None
        except Exception as e:
            print(f"❌ Receive error: {e}")
            return None
    
    def subscribe(self) -> bool:
        """Subscribe to mining notifications"""
        print("\n🔔 Subscribing to mining...")
        if not self.send_message("mining.subscribe", ["SimpleMiner/1.0"]):
            return False
        
        response = self.receive_message()
        if not response or 'result' not in response:
            print("❌ Subscribe failed")
            return False
        
        result = response['result']
        if len(result) >= 2:
            self.extranonce1 = result[1]
            self.extranonce2_size = result[2] if len(result) > 2 else 4
            print(f"✅ Subscribed! Extranonce1: {self.extranonce1}, Size: {self.extranonce2_size}")
            return True
        
        print("❌ Invalid subscribe response")
        return False
    
    def authorize(self) -> bool:
        """Authorize worker"""
        print(f"\n🔐 Authorizing worker: {self.username}")
        if not self.send_message("mining.authorize", [self.username, self.password]):
            return False
        
        response = self.receive_message()
        if response and response.get('result') == True:
            print("✅ Worker authorized!")
            return True
        
        print(f"❌ Authorization failed: {response}")
        return False
    
    def handle_mining_notify(self, params):
        """Handle mining.notify message"""
        print("\n📢 New work received!")
        
        if len(params) >= 8:
            self.job_id = params[0]
            self.prevhash = params[1]
            self.coinb1 = params[2]
            self.coinb2 = params[3]
            self.merkle_branch = params[4]
            self.version = params[5]
            self.nbits = params[6]
            self.ntime = params[7]
            clean_jobs = params[8] if len(params) > 8 else False
            
            print(f"Job ID: {self.job_id}")
            print(f"Previous Hash: {self.prevhash}")
            print(f"Version: {self.version}")
            print(f"nBits: {self.nbits}")
            print(f"nTime: {self.ntime}")
            print(f"Clean Jobs: {clean_jobs}")
            
            return True
        
        print("❌ Invalid mining.notify parameters")
        return False
    
    def handle_set_difficulty(self, params):
        """Handle mining.set_difficulty message"""
        if params and len(params) > 0:
            self.difficulty = params[0]
            print(f"🎯 Difficulty set to: {self.difficulty}")
    
    def create_coinbase(self, extranonce2: str) -> str:
        """Create coinbase transaction"""
        if not self.coinb1 or not self.coinb2 or not self.extranonce1:
            return ""
        
        # Pad extranonce2 to correct size
        extranonce2 = extranonce2.ljust(self.extranonce2_size * 2, '0')
        coinbase = self.coinb1 + self.extranonce1 + extranonce2 + self.coinb2
        return coinbase
    
    def calculate_merkle_root(self, coinbase: str) -> str:
        """Calculate merkle root"""
        if not coinbase:
            return ""
        
        # Hash coinbase transaction
        coinbase_bin = binascii.unhexlify(coinbase)
        coinbase_hash = hashlib.sha256(hashlib.sha256(coinbase_bin).digest()).digest()
        
        # Calculate merkle root
        merkle_root = coinbase_hash
        for branch in self.merkle_branch:
            branch_bin = binascii.unhexlify(branch)
            merkle_root = hashlib.sha256(hashlib.sha256(merkle_root + branch_bin).digest()).digest()
        
        return binascii.hexlify(merkle_root).decode()
    
    def create_block_header(self, merkle_root: str, nonce: int) -> str:
        """Create block header for hashing"""
        if not all([self.version, self.prevhash, merkle_root, self.ntime, self.nbits]):
            return ""
        
        # Reverse byte order for little-endian
        version = struct.pack("<I", int(self.version, 16)).hex()
        prevhash = ''.join([self.prevhash[i:i+2] for i in range(0, len(self.prevhash), 2)][::-1])
        merkle = ''.join([merkle_root[i:i+2] for i in range(0, len(merkle_root), 2)][::-1])
        ntime = struct.pack("<I", int(self.ntime, 16)).hex()
        nbits = struct.pack("<I", int(self.nbits, 16)).hex()
        nonce_hex = struct.pack("<I", nonce).hex()
        
        header = version + prevhash + merkle + ntime + nbits + nonce_hex
        return header
    
    def mine_simple(self, max_nonce: int = 1000000):
        """Simple mining function (very basic, not optimized)"""
        if not self.job_id:
            print("❌ No job available for mining")
            return
        
        print(f"\n⛏️ Starting simple mining (max nonce: {max_nonce})...")
        
        extranonce2 = "00000000"  # Simple fixed extranonce2
        coinbase = self.create_coinbase(extranonce2)
        merkle_root = self.calculate_merkle_root(coinbase)
        
        if not merkle_root:
            print("❌ Failed to create merkle root")
            return
        
        target = 2**(256 - int(self.difficulty.bit_length()))
        print(f"Target difficulty: {self.difficulty}")
        
        start_time = time.time()
        for nonce in range(max_nonce):
            if not self.running:
                break
            
            header = self.create_block_header(merkle_root, nonce)
            if not header:
                continue
            
            # Double SHA256
            header_bin = binascii.unhexlify(header)
            hash_result = hashlib.sha256(hashlib.sha256(header_bin).digest()).digest()
            hash_int = int.from_bytes(hash_result, 'big')
            
            if nonce % 10000 == 0:
                elapsed = time.time() - start_time
                hashrate = nonce / elapsed if elapsed > 0 else 0
                print(f"⚡ Nonce: {nonce:8d}, Hashrate: {hashrate:8.0f} H/s")
            
            # Check if we found a valid share (simplified)
            if hash_int < target * 1000:  # Much easier target for demo
                print(f"🎉 Potential share found! Nonce: {nonce}")
                self.submit_share(extranonce2, self.ntime, nonce)
                break
        
        elapsed = time.time() - start_time
        avg_hashrate = max_nonce / elapsed if elapsed > 0 else 0
        print(f"⏱️ Mining completed. Average hashrate: {avg_hashrate:.0f} H/s")
    
    def submit_share(self, extranonce2: str, ntime: str, nonce: int):
        """Submit mining share"""
        nonce_hex = f"{nonce:08x}"
        params = [self.username, self.job_id, extranonce2, ntime, nonce_hex]
        
        print(f"📤 Submitting share: {params}")
        if self.send_message("mining.submit", params):
            self.shares_submitted += 1
            
            response = self.receive_message()
            if response:
                if response.get('result') == True:
                    self.shares_accepted += 1
                    print("✅ Share accepted!")
                else:
                    self.shares_rejected += 1
                    print(f"❌ Share rejected: {response.get('error')}")
    
    def listen_loop(self):
        """Main listening loop for pool messages"""
        while self.running:
            try:
                self.socket.settimeout(1)
                message = self.receive_message()
                if not message:
                    continue
                
                if 'method' in message:
                    method = message['method']
                    params = message.get('params', [])
                    
                    if method == 'mining.notify':
                        self.handle_mining_notify(params)
                    elif method == 'mining.set_difficulty':
                        self.handle_set_difficulty(params)
                    else:
                        print(f"🔔 Unknown method: {method}")
                
            except socket.timeout:
                continue
            except Exception as e:
                print(f"❌ Listen error: {e}")
                break
    
    def start_mining(self, duration: int = 60):
        """Start the mining process"""
        if not self.connect():
            return False
        
        if not self.subscribe():
            return False
        
        if not self.authorize():
            return False
        
        self.running = True
        
        # Start listening thread
        listen_thread = threading.Thread(target=self.listen_loop)
        listen_thread.daemon = True
        listen_thread.start()
        
        # Wait for initial work
        print("\n⏳ Waiting for mining work...")
        time.sleep(5)
        
        # Start mining
        if self.job_id:
            self.mine_simple(max_nonce=100000)
        else:
            print("❌ No mining work received")
        
        # Keep listening for a bit more
        time.sleep(10)
        
        self.running = False
        self.socket.close()
        
        print(f"\n📊 Mining Statistics:")
        print(f"Shares Submitted: {self.shares_submitted}")
        print(f"Shares Accepted: {self.shares_accepted}")
        print(f"Shares Rejected: {self.shares_rejected}")
        
        return True

def main():
    print("🚀 Simple Bitcoin Stratum Miner")
    print("=" * 50)
    
    # Test with different pools
    pools = [
        ("stratum-btc.antpool.com", 3333, "test_user.worker1"),
        ("btc-eu.f2pool.com", 1314, "test_user.worker1"),
        ("eu.stratum.slushpool.com", 3333, "test_user.worker1"),
    ]
    
    for host, port, username in pools:
        print(f"\n🏊‍♂️ Testing pool: {host}:{port}")
        print("-" * 40)
        
        miner = StratumMiner(host, port, username)
        success = miner.start_mining(duration=30)
        
        if not success:
            print(f"❌ Failed to mine on {host}:{port}")
        
        time.sleep(2)

if __name__ == "__main__":
    main()
